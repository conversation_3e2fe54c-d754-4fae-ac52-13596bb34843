# Quantum Project
https://discord.gg/kJ8ZrGM8TS

RSG Framework Butcher, this allows you to take carcass of an animal to the butcher and get money & raw meat in return. 

MAKE SURE YOU ARE ON **RELEASE** NOT LATEST IN YOUR GAME CLIENT UNTIL I UPDATE THE MENU

# Dependancies
- rsg-core
- rsg-menu

# Preview
![image](https://github.com/Artmines/qc-butcher/assets/96462463/613aeeac-3e7a-455d-804b-ee60fc70c969)

![image](https://github.com/Artmines/qc-butcher/assets/96462463/a3a146b2-c849-435e-bd32-04093617b2a3)


# Installation:
- ensure that the dependancies are added and started
- add qc-butcher to your resources folder
- add items to your "\rsg-core\shared\items.lua"
- add images to your "\rsg-inventory\html\images"

# Starting the resource:
- add the following to your server.cfg file : ensure qc-butcher

# Giving Credit where Due
- Thanks to RSG Core Team
