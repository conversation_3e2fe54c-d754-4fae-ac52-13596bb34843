[{"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-fishing/@ox_lib/init.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-fishing/client/client.lua", "mt": 1738233509, "s": 30096, "i": "KQkAAAAAAgAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-fishing/client/client_js.js", "mt": 1738233509, "s": 3307, "i": "KgkAAAAAAgAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-fishing/config.lua", "mt": 1738233509, "s": 11803, "i": "KwkAAAAAAgAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-fishing/fxmanifest.lua", "mt": 1738233509, "s": 614, "i": "LAkAAAAAAgAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-fishing/locales/el.json", "mt": 1738233509, "s": 1015, "i": "LQkAAAAAAgAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-fishing/locales/en.json", "mt": 1738233509, "s": 624, "i": "LgkAAAAAAgAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-fishing/locales/es.json", "mt": 1738233509, "s": 772, "i": "LwkAAAAAAgAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-fishing/locales/fr.json", "mt": 1738233509, "s": 805, "i": "MAkAAAAAAgAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-fishing/locales/it.json", "mt": 1738233509, "s": 728, "i": "MQkAAAAAAgAAAAAAAAAAAA=="}]