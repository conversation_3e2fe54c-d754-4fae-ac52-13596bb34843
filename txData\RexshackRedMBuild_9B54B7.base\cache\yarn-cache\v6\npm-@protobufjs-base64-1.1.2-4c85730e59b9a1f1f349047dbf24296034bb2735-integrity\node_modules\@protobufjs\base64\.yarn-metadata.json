{"manifest": {"name": "@protobufjs/base64", "description": "A minimal base64 implementation for number arrays.", "version": "1.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/dcodeIO/protobuf.js.git"}, "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "types": "index.d.ts", "devDependencies": {"istanbul": "^0.4.5", "tape": "^4.6.3"}, "scripts": {"test": "tape tests/*.js", "coverage": "istanbul cover node_modules/tape/bin/tape tests/*.js"}, "_registry": "npm", "_loc": "B:\\txData\\RexshackRedMBuild_9B54B7.base\\cache\\yarn-cache\\v6\\npm-@protobufjs-base64-1.1.2-4c85730e59b9a1f1f349047dbf24296034bb2735-integrity\\node_modules\\@protobufjs\\base64\\package.json", "readmeFilename": "README.md", "readme": "@protobufjs/base64\n==================\n[![npm](https://img.shields.io/npm/v/@protobufjs/base64.svg)](https://www.npmjs.com/package/@protobufjs/base64)\n\nA minimal base64 implementation for number arrays.\n\nAPI\n---\n\n* **base64.length(string: `string`): `number`**<br />\n  Calculates the byte length of a base64 encoded string.\n\n* **base64.encode(buffer: `Uint8Array`, start: `number`, end: `number`): `string`**<br />\n  Encodes a buffer to a base64 encoded string.\n\n* **base64.decode(string: `string`, buffer: `Uint8Array`, offset: `number`): `number`**<br />\n  Decodes a base64 encoded string to a buffer.\n\n**License:** [BSD 3-Clause License](https://opensource.org/licenses/BSD-3-Clause)\n", "licenseText": "Copyright (c) 2016, <PERSON>  All rights reserved.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are\nmet:\n\n* Redistributions of source code must retain the above copyright\n  notice, this list of conditions and the following disclaimer.\n* Redistributions in binary form must reproduce the above copyright\n  notice, this list of conditions and the following disclaimer in the\n  documentation and/or other materials provided with the distribution.\n* Neither the name of its author, nor the names of its contributors\n  may be used to endorse or promote products derived from this software\n  without specific prior written permission.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n\"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\nLIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\nA PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\nOWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\nSPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIA<PERSON> DAMAGES (INCLUDING, BUT NOT\nLIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\nDATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\nTHEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\nOF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@protobufjs/base64/-/base64-1.1.2.tgz#4c85730e59b9a1f1f349047dbf24296034bb2735", "type": "tarball", "reference": "https://registry.yarnpkg.com/@protobufjs/base64/-/base64-1.1.2.tgz", "hash": "4c85730e59b9a1f1f349047dbf24296034bb2735", "integrity": "sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==", "registry": "npm", "packageName": "@protobufjs/base64", "cacheIntegrity": "sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg== sha1-TIVzDlm5ofHzSQR9vyQpYDS7JzU="}, "registry": "npm", "hash": "4c85730e59b9a1f1f349047dbf24296034bb2735"}