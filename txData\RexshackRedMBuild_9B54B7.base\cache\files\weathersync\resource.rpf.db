[{"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[standalone]/weathersync/client.lua", "mt": 1738233417, "s": 17385, "i": "SgQAAAAAAgAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[standalone]/weathersync/config.lua", "mt": 1738233417, "s": 5764, "i": "SwQAAAAAAgAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[standalone]/weathersync/fxmanifest.lua", "mt": 1738233417, "s": 581, "i": "TAQAAAAAAgAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[standalone]/weathersync/shared.lua", "mt": 1738233417, "s": 1478, "i": "TwQAAAAAAgAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[standalone]/weathersync/ui/CHINESER.TTF", "mt": 1738233417, "s": 52728, "i": "UAQAAAAAAgAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[standalone]/weathersync/ui/index.html", "mt": 1738233417, "s": 8836, "i": "UQQAAAAAAgAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[standalone]/weathersync/ui/script.js", "mt": 1738233417, "s": 9695, "i": "UgQAAAAAAgAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[standalone]/weathersync/ui/style.css", "mt": 1738233417, "s": 3770, "i": "UwQAAAAAAgAAAAAAAAAAAA=="}]