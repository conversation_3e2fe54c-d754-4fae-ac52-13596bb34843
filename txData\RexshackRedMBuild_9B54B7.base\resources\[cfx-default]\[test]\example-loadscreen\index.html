<html>
    <head>
        <link href="keks.css" rel="stylesheet" type="text/css" />
    </head>
    <body>
        <div class="backdrop">
            <div class="top">
                <h1 title="Free Mode">Free Mode</h1>
                <h2 title="Not Algonquin">Not Algonquin</h2>
            </div>

            <div class="letni">
                <h2 title="INTEL">Intel</h2>
                <h3></h3>
                <div class="loadbar"><div class="thingy"></div></div>
                <p>The Statue of Happiness has no heart. You do.</p>
            </div>
            <div class="bottom">
                <div id="gradient">
                </div>
            </div>
        </div>

<script type="text/javascript">
// this will actually restart the loading bar a lot, making multiple loading bars is an exercise to the reader... for now.
// for a set of possible events, see https://github.com/citizenfx/fivem/blob/master/code/components/loading-screens-five/src/LoadingScreens.cpp
var count = 0;
var thisCount = 0;

const emoji = {
    INIT_BEFORE_MAP_LOADED: [ '🍉' ],
    INIT_AFTER_MAP_LOADED: [ '🍋', '🍊' ],
    INIT_SESSION: [ '🍐', '🍅', '🍆' ],
};

const handlers = {
    startInitFunctionOrder(data)
    {
        count = data.count;

        document.querySelector('.letni h3').innerHTML += emoji[data.type][data.order - 1] || '';
    },

    initFunctionInvoking(data)
    {
        document.querySelector('.thingy').style.left = '0%';
        document.querySelector('.thingy').style.width = ((data.idx / count) * 100) + '%';
    },

    startDataFileEntries(data)
    {
        count = data.count;

        document.querySelector('.letni h3').innerHTML += "\u{1f358}";
    },

    performMapLoadFunction(data)
    {
        ++thisCount;

        document.querySelector('.thingy').style.left = '0%';
        document.querySelector('.thingy').style.width = ((thisCount / count) * 100) + '%';
    },

    onLogLine(data)
    {
        document.querySelector('.letni p').innerHTML = data.message + "..!";
    }
};

window.addEventListener('message', function(e)
{
    (handlers[e.data.eventName] || function() {})(e.data);
});
</script>
    </body>
</html>
