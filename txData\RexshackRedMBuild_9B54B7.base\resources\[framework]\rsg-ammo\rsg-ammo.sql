CREATE TABLE `player_ammo` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`citizenid` VARCHAR(255) NOT NULL,
	`ammo_revolver` INT(3) NOT NULL DEFAULT '0',
	`ammo_revolver_express` INT(3) NOT NULL DEFAULT '0',
	`ammo_revolver_express_explosive` INT(3) NOT NULL DEFAULT '0',
	`ammo_revolver_high_velocity` INT(3) NOT NULL DEFAULT '0',
	`ammo_revolver_split_point` INT(3) NOT NULL DEFAULT '0',
	`ammo_pistol` INT(3) NOT NULL DEFAULT '0',
	`ammo_pistol_express` INT(3) NOT NULL DEFAULT '0',
	`ammo_pistol_express_explosive` INT(3) NOT NULL DEFAULT '0',
	`ammo_pistol_high_velocity` INT(3) NOT NULL DEFAULT '0',
	`ammo_pistol_split_point` INT(3) NOT NULL DEFAULT '0',
	`ammo_repeater` INT(3) NOT NULL DEFAULT '0',
	`ammo_repeater_express` INT(3) NOT NULL DEFAULT '0',
	`ammo_repeater_express_explosive` INT(3) NOT NULL DEFAULT '0',
	`ammo_repeater_high_velocity` INT(3) NOT NULL DEFAULT '0',
	`ammo_repeater_split_point` INT(3) NOT NULL DEFAULT '0',
	`ammo_rifle` INT(3) NOT NULL DEFAULT '0',
	`ammo_rifle_express` INT(3) NOT NULL DEFAULT '0',
	`ammo_rifle_express_explosive` INT(3) NOT NULL DEFAULT '0',
	`ammo_rifle_high_velocity` INT(3) NOT NULL DEFAULT '0',
	`ammo_rifle_split_point` INT(3) NOT NULL DEFAULT '0',
	`ammo_shotgun` INT(3) NOT NULL DEFAULT '0',
	`ammo_shotgun_buckshot_incendiary` INT(3) NOT NULL DEFAULT '0',
	`ammo_shotgun_slug` INT(3) NOT NULL DEFAULT '0',
	`ammo_shotgun_slug_explosive` INT(3) NOT NULL DEFAULT '0',
	`ammo_rifle_elephant` INT(3) NOT NULL DEFAULT '0',
	`ammo_22` INT(3) NOT NULL DEFAULT '0',
	`ammo_22_tranquilizer` INT(3) NOT NULL DEFAULT '0',
	`ammo_arrow` INT(3) NOT NULL DEFAULT '0',
	`ammo_arrow_small_game` INT(3) NOT NULL DEFAULT '0',
	`ammo_arrow_fire` INT(3) NOT NULL DEFAULT '0',
	`ammo_arrow_poison` INT(3) NOT NULL DEFAULT '0',
	`ammo_arrow_dynamite` INT(3) NOT NULL DEFAULT '0',
	`ammo_molotov` INT(3) NOT NULL DEFAULT '0',
	`ammo_tomahawk` INT(3) NOT NULL DEFAULT '0',
	`ammo_tomahawk_ancient` INT(3) NOT NULL DEFAULT '0',
	`ammo_dynamite` INT(3) NOT NULL DEFAULT '0',
	`ammo_poisonbottle` INT(3) NOT NULL DEFAULT '0',
	`ammo_throwing_knives` INT(3) NOT NULL DEFAULT '0',
	`ammo_throwing_knives_drain` INT(3) NOT NULL DEFAULT '0',
	`ammo_throwing_knives_poison` INT(3) NOT NULL DEFAULT '0',
	`ammo_bolas` INT(3) NOT NULL DEFAULT '0',
	`ammo_bolas_hawkmoth` INT(3) NOT NULL DEFAULT '0',
	`ammo_bolas_intertwined` INT(3) NOT NULL DEFAULT '0',
	`ammo_bolas_ironspiked` INT(3) NOT NULL DEFAULT '0',
	`ammo_hatchet` INT(3) NOT NULL DEFAULT '0',
	`ammo_hatchet_hunter` INT(3) NOT NULL DEFAULT '0',
	`ammo_hatchet_cleaver` INT(3) NOT NULL DEFAULT '0',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `citizenid` (`citizenid`) USING BTREE
)
ENGINE=InnoDB
;
