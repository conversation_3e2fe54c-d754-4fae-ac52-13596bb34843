{"cl_client_0": "<PERSON><PERSON>", "cl_client_1": "voir les options d'administration", "cl_client_2": "Options du Joueur", "cl_client_3": "voir les options du joueur", "cl_client_88": "Finances du Joueur", "cl_client_89": "ajuster les finances du joueur", "cl_client_4": "Options de Troll", "cl_client_5": "voir les options de troll", "cl_client_6": "<PERSON><PERSON><PERSON> Serveur", "cl_client_7": "voir les options du serveur", "cl_client_8": "Options Développeur", "cl_client_9": "voir les options développeur", "cl_client_10": "Menu Options Admin", "cl_client_11": "Se téléporter au point", "cl_client_12": "vous devez avoir un point défini avant de faire cela", "cl_client_13": "Réanimation Automatique", "cl_client_14": "se réanimer automatiquement", "cl_client_15": "Activer l'Invisibilité", "cl_client_16": "basculer l'invisibilité", "cl_client_146": "Afficher les IDs", "cl_client_147": "afficher/masquer les IDs des joueurs", "cl_client_17": "God Mode", "cl_client_18": "activer/d<PERSON><PERSON><PERSON> le <PERSON>", "cl_client_19": "ID:", "cl_client_21": "<PERSON><PERSON>", "cl_client_137": "<PERSON><PERSON><PERSON>", "cl_client_138": "obtenir les informations des joueurs", "cl_client_22": "<PERSON><PERSON><PERSON><PERSON> le Joueur", "cl_client_23": "r<PERSON><PERSON>mer ce joueur", "cl_client_130": "Donner un Objet", "cl_client_131": "donner un objet à un joueur", "cl_client_24": "Inventaire du Joueur", "cl_client_25": "ouvrir l'inventaire du joueur, appuyez sur [I] pour naviguer", "cl_client_26": "Expulser le Joueur", "cl_client_27": "expulser un joueur du serveur avec une raison", "cl_client_28": "Ban<PERSON> le Joueur", "cl_client_29": "bannir un joueur du serveur avec une raison", "cl_client_30": "<PERSON><PERSON>ur", "cl_client_31": "se téléporter vers un joueur", "cl_client_32": "Amener le Joueur", "cl_client_33": "amener un joueur à votre position", "cl_client_34": "<PERSON><PERSON>", "cl_client_35": "activer/désactiver le gel du joueur", "cl_client_36": "Observer le Joueur", "cl_client_37": "activer/désactiver l'observation du joueur", "cl_client_38": "<PERSON><PERSON>", "cl_client_39": "<PERSON><PERSON><PERSON><PERSON> Serveur", "cl_client_40": "ajuster la météo du serveur", "cl_adminmenu": "Options Admin", "cl_adminmenu_a": "basculé > INVISIBILITÉ <", "cl_client_42": "Invisibilité Activée", "cl_client_43": "vous êtes maintenant invisible", "cl_client_44": "Invisibilité Désactivée", "cl_client_45": "vous êtes maintenant visible", "cl_client_46": "God Mode Activé", "cl_client_47": "le God Mode est activé", "cl_adminmenu_b": "basculé > GOD MODE <", "cl_client_48": "God Mode Désactivé", "cl_client_49": "le God Mode est désactivé", "cl_client_50": "Expulser le Joueur", "cl_client_51": "<PERSON>son", "cl_client_52": "Ban<PERSON> le Joueur", "cl_client_53": "Type de Ban", "cl_client_53_a": "Permanent", "cl_client_53_b": "Temporaire", "cl_client_54": "Durée du Ban", "cl_client_55": "1 Heure", "cl_client_56": "6 Heures", "cl_client_57": "12 Heures", "cl_client_58": "1 Jour", "cl_client_59": "3 Jours", "cl_client_60": "1 Semaine", "cl_client_61": "1 Mois", "cl_client_62": "3 Mois", "cl_client_63": "6 Mois", "cl_client_64": "1 An", "cl_client_65": "Permanent", "cl_client_66": "<PERSON><PERSON><PERSON>", "cl_client_67": "a été banni définitivement", "cl_client_68": "a reçu un ban temporaire", "cl_client_132": "Donner un Objet au Joueur", "cl_client_133": "Objet de l'Inventaire", "cl_client_134": "Quantité", "cl_client_139": "<PERSON><PERSON><PERSON>", "cl_client_140": "Nom", "cl_client_141": "<PERSON><PERSON><PERSON>", "cl_client_142": "Grade du Métier", "cl_client_143": "Argent", "cl_client_144": "Argent Sale", "cl_client_153": "Banque", "cl_client_148": "Banque Val", "cl_client_149": "Banque Rho", "cl_client_150": "Banque Blk", "cl_client_151": "Banque Arm", "cl_anim_test": "Testeur d'Animation", "cl_anim_dictionary": "Dictionnaire d'Animations", "cl_anim_name": "Nom de l'Animation", "cl_anim_flag": "<PERSON><PERSON><PERSON>", "cl_anim_length": "Durée: en millisecondes", "cl_coords_menu": "Copier les Coordonnées", "cl_coords_70": "Copier le Vecteur 2", "cl_coords_71": "copier les coordonnées du vecteur2", "cl_coords_72": "Copier le Vecteur 3", "cl_coords_73": "copier les coordonnées du vecteur3", "cl_coords_74": "Copier le Vecteur 4", "cl_coords_75": "copier les coordonnées du vecteur4", "cl_coords_76": "Copier l'Orientation", "cl_coords_77": "copier l'orientation", "cl_coords_print_list": "Impression de la Liste Activée", "cl_coords_print_list_a": "Activer l'impression des coordonnées dans une liste", "cl_coords_print_full": "Imprimer la Liste Complète", "cl_coords_print_full_a": "Imprimer la liste complète des coordonnées", "cl_coords_78": "Coordonnées Copiées", "cl_coords_79": "les coordonnées du vecteur2 ont été copiées dans le presse-papiers", "cl_coords_80": "les coordonnées du vecteur3 ont été copiées dans le presse-papiers", "cl_coords_81": "les coordonnées du vecteur4 ont été copiées dans le presse-papiers", "cl_coords_82": "Orientation Copiée", "cl_coords_83": "l'orientation a été copiée dans le presse-papiers", "cl_coords_printlist": "Imprimer la Liste", "cl_coords_printlist_a": "Impression de la liste activée. Les coordonnées seront ajoutées à la liste.", "cl_coords_printlist_b": "La liste a été copiée dans le presse-papiers", "cl_coords_printlist_c": "La liste est vide", "cl_dev_menu": "<PERSON><PERSON>", "cl_dev_menu_a": "Faire Apparaître un Cheval Admin", "cl_dev_menu_b": "faire apparaître un cheval admin", "cl_dev_menu_c": "Menu Co<PERSON>r les Coordonnées", "cl_dev_menu_d": "copier les coordonnées dans le presse-papiers", "cl_dev_menu_e": "Testeur d'Animations", "cl_dev_menu_f": "tester les animations", "cl_dev_menu_g": "Hash de l'Entité", "cl_dev_menu_h": "obtenir le hash de l'entité", "cl_dev_menu_i": "Afficher les IDs des Portes", "cl_dev_menu_j": "utilisé pour obtenir les IDs des portes", "cl_dev_menu_l": "Générateur de PNJ", "cl_dev_menu_m": "utilisé pour générer des PNJ/animaux", "cl_dev_126": "Faire Apparaître un Cheval Admin", "cl_dev_127": "<PERSON><PERSON><PERSON><PERSON> le Hash de l'Entité", "cl_dev_128": "nom de l'entité", "cl_dev_129": "exemple: PROVISION_ALLIGATOR_SKIN", "cl_dev_copy": "Hash de l'Entité Copié", "cl_dev_copy_a": "le hash de l'entité", "cl_dev_copy_b": "a été copié dans le presse-papiers", "cl_dev_spawnped": "Générer un PNJ/Animal", "cl_dev_spawnped1": "nom du PNJ", "cl_dev_spawnped2": "exemple: mp_a_c_wolf_01", "cl_dev_spawnped3": "tenue", "cl_dev_spawnped4": "numéro de la tenue pour le PNJ/animal", "cl_dev_spawnped5": "distance", "cl_dev_spawnped6": "distance de génération par rapport à vous", "cl_dev_spawnped7": "geler", "cl_dev_spawnped8": "geler le PNJ/animal à l'apparition", "cl_dev_spawnped9": "Vrai", "cl_dev_spawnped10": "Faux", "cl_dev_spawnped11": "g<PERSON><PERSON><PERSON> mort", "cl_dev_spawnped12": "générer le PNJ/animal mort", "cl_door_on": "IDs des Portes Activés", "cl_door_off": "IDs des Portes Désactivés", "cl_door_id": "ID de la Porte", "cl_door_o": "Objet", "cl_finan_menu": "Finances du Joueur", "cl_finan_19": "ID:", "cl_finan_90": "Menu des Options Financières", "cl_finan_122": "Banque: $", "cl_finan_122_a": "Banque Val: $", "cl_finan_122_b": "Banque Rho: $", "cl_finan_122_c": "Banque Blk: $", "cl_finan_122_d": "Banque Arm: $", "cl_finan_123": "Argent: $", "cl_finan_123_a": "Argent sale: $", "cl_finan_119": "solde bancaire actuel du joueur", "cl_finan_120": "solde en espèces actuel du joueur", "cl_finan_121": "solde d'argent sale actuel du joueur", "cl_finan_91": "<PERSON><PERSON> l'Argent", "cl_finan_92": "donner de l'argent au joueur", "cl_finan_93": "Re<PERSON>rer de l'Argent", "cl_finan_94": "<PERSON>r de l'argent au joueur", "cl_finan_95": "Type", "cl_finan_96": "choisir le type à donner au joueur", "cl_finan_2": "Banque Standard", "cl_finan_2a": "Banque Val", "cl_finan_2b": "Banque Rho", "cl_finan_2c": "Banque Blk", "cl_finan_2d": "Banque Arm", "cl_finan_3": "Espèces", "cl_finan_3a": "Argent sale", "cl_finan_97": "<PERSON><PERSON>", "cl_finan_98": "combien souhaitez-vous donner?", "cl_finan_99": "choisir le type à retirer au joueur", "cl_finan_115": "<PERSON>ien souhaitez-vous retirer?", "cl_troll_19": "ID", "cl_troll_84": "<PERSON><PERSON><PERSON> <PERSON>", "cl_troll_85": "Menu des Options de Troll", "cl_troll_86": "Attaque Sauvage", "cl_troll_87": "troller le joueur en activant une attaque sauvage", "cl_troll_128": "Mettre le Joueur en Feu", "cl_troll_129": "enflammer un joueur", "sv_a": "<PERSON><PERSON><PERSON>", "sv_b": "%s a été banni par %s pour %s", "sv_c": "le système vous a banni pour usage inapproprié", "sv_105": "Vous avez été banni définitivement par le serveur pour: Exploitation", "sv_100": "ouvrir le menu admin (Admin Seulement)", "sv_d": "Utilisation non autorisée du Menu Admin", "sv_e": "avec l'ID de citoyen de", "sv_f": "a tenté d'utiliser le menu admin", "sv_101": "Non Autorisé", "sv_102": "vous n'êtes pas autorisé à faire cela!", "sv_g": "Utilisation Non Autorisée", "sv_h": "avec l'ID de citoyen de", "sv_i": "banni pour avoir utilisé la réanimation admin", "sv_j": "banni pour avoir ouvert l'inventaire", "sv_kicked": "<PERSON><PERSON><PERSON>pu<PERSON>", "sv_kicked_a": "%s a été expulsé par %s pour %s", "sv_kicked_b": "banni pour avoir utilisé l'expulsion de joueur", "sv_ban": "ANNONCE", "sv_ban_a": "<PERSON><PERSON><PERSON>", "sv_ban_b": "a été banni", "sv_ban_c": "banni pour avoir utilisé l'aller au joueur", "sv_ban_d": "banni pour avoir utilisé l'amener joueur", "sv_ban_e": "banni pour avoir utilisé le gel du joueur", "sv_ban_f": "banni pour avoir utilisé l'observation du joueur", "sv_ban_g": "banni pour avoir utilisé l'attaque sauvage", "sv_ban_h": "banni pour avoir utilisé l'enflammer", "sv_ban_i": "banni pour avoir utilisé le donner d'objet", "sv_ban_j": "banni pour avoir utilisé l'obtention d'infos joueur", "sv_103": "V<PERSON> avez été expulsé du serveur", "sv_104": "🔸 Consultez notre Discord pour plus d'informations:", "sv_106": "<PERSON><PERSON> avez <PERSON> banni:", "sv_107": "Votre ban est permanent.", "sv_108": "🔸 Consultez notre Discord pour plus d'informations:", "sv_109": "Le ban expire:", "sv_110": "🔸 Consultez notre Discord pour plus d'informations:", "sv_111": "<PERSON><PERSON> du Joueur Activé", "sv_112": "vous avez gelé le joueur", "sv_113": "Gel du Joueur Désactivé", "sv_114": "vous avez dégé<PERSON> le joueur", "sv_135": "<PERSON>b<PERSON>", "sv_136": "l'objet a été envoyé avec succès", "sv_finan_116": "<PERSON><PERSON><PERSON>", "sv_finan_117": "le joueur n'a pas assez de", "sv_finan_118": "pour retirer!"}