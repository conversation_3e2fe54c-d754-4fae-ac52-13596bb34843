{"manifest": {"name": "@types/node", "version": "10.17.58", "description": "TypeScript definitions for Node.js", "license": "MIT", "contributors": [{"name": "Microsoft TypeScript", "url": "https://github.com/Microsoft"}, {"name": "DefinitelyTyped", "url": "https://github.com/DefinitelyTyped"}, {"name": "<PERSON>", "url": "https://github.com/jkomyno"}, {"name": "Alvis <PERSON>", "url": "https://github.com/alvis"}, {"name": "<PERSON>", "url": "https://github.com/r3nya"}, {"name": "<PERSON>", "url": "https://github.com/bruno<PERSON>ufler"}, {"name": "Chigozirim C.", "url": "https://github.com/smac89"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/DeividasBakanas"}, {"name": "<PERSON>", "url": "https://github.com/eyqs"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>-<PERSON>-CK"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/KSXGitHub"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/hoo29"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kjin"}, {"name": "<PERSON>", "url": "https://github.com/ajafff"}, {"name": "Lishude", "url": "https://github.com/islishude"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mwiktorczyk"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/mohsen1"}, {"name": "<PERSON>", "url": "https://github.com/n-e"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/galkin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/parambirs"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick"}, {"name": "<PERSON>", "url": "https://github.com/ThomasdenH"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/WilcoBakker"}, {"name": "wwwy3y3", "url": "https://github.com/wwwy3y3"}, {"name": "<PERSON>", "url": "https://github.com/ZaneHannanAU"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/jeremie<PERSON>z"}, {"name": "<PERSON>", "url": "https://github.com/samuela"}, {"name": "<PERSON>", "url": "https://github.com/kuehlein"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/bhongy"}, {"name": "<PERSON>", "url": "https://github.com/nguymin4"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=3.6": {"*": ["ts3.6/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "fc8b202fb70125505792af7ce55808619dc40e9dd39e1a586623c58e5b1a4755", "typeScriptVersion": "3.5", "_registry": "npm", "_loc": "B:\\txData\\RexshackRedMBuild_9B54B7.base\\cache\\yarn-cache\\v6\\npm-@types-node-10.17.58-10682f6016fd866725c36d22ce6bbbd029bf4545-integrity\\node_modules\\@types\\node\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/node`\n\n# Summary\nThis package contains type definitions for Node.js (http://nodejs.org/).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node/v10.\n\n### Additional Details\n * Last updated: Thu, 15 Apr 2021 17:31:28 GMT\n * Dependencies: none\n * Global values: `<PERSON><PERSON><PERSON>`, `NodeJ<PERSON>`, `__dirname`, `__filename`, `clearImmediate`, `clearInterval`, `clearTimeout`, `console`, `exports`, `global`, `module`, `process`, `require`, `setImmediate`, `setInterval`, `setTimeout`\n\n# Credits\nThese definitions were written by [Microsoft TypeScript](https://github.com/Microsoft), [DefinitelyTyped](https://github.com/DefinitelyTyped), [<PERSON>](https://github.com/jkomyno), [<PERSON><PERSON>T <PERSON>](https://github.com/alvis), [<PERSON>](https://github.com/r3nya), [<PERSON>](https://github.com/bruno<PERSON><PERSON><PERSON>), [Chigo<PERSON> C.](https://github.com/smac89), [Deividas Bakanas](https://github.com/DeividasBakanas), [Eugene Y. Q. Shen](https://github.com/eyqs), [Hannes Magnusson](https://github.com/Hannes-Magnusson-CK), [Hoàng Văn Khải](https://github.com/KSXGitHub), [Huw](https://github.com/hoo29), [Kelvin Jin](https://github.com/kjin), [Klaus Meinhardt](https://github.com/ajafff), [Lishude](https://github.com/islishude), [Mariusz Wiktorczyk](https://github.com/mwiktorczyk), [Mohsen Azimi](https://github.com/mohsen1), [Nicolas Even](https://github.com/n-e), [Nikita Galkin](https://github.com/galkin), [Parambir Singh](https://github.com/parambirs), [Sebastian Silbermann](https://github.com/eps1lon), [Simon Schick](https://github.com/SimonSchick), [Thomas den Hollander](https://github.com/ThomasdenH), [Wilco Bakker](https://github.com/WilcoBakker), [wwwy3y3](https://github.com/wwwy3y3), [Zane Hannan AU](https://github.com/ZaneHannanAU), [Jeremie Rodriguez](https://github.com/jeremiergz), [Samuel Ainsworth](https://github.com/samuela), [Kyle Uehlein](https://github.com/kuehlein), [Thanik Bhongbhibhat](https://github.com/bhongy), [Minh Son Nguyen](https://github.com/nguymin4), and [ExE Boss](https://github.com/ExE-Boss).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/node/-/node-10.17.58.tgz#10682f6016fd866725c36d22ce6bbbd029bf4545", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/node/-/node-10.17.58.tgz", "hash": "10682f6016fd866725c36d22ce6bbbd029bf4545", "integrity": "sha512-Dn5RBxLohjdHFj17dVVw3rtrZAeXeWg+LQfvxDIW/fdPkSiuQk7h3frKMYtsQhtIW42wkErDcy9UMVxhGW4O7w==", "registry": "npm", "packageName": "@types/node", "cacheIntegrity": "sha512-Dn5RBxLohjdHFj17dVVw3rtrZAeXeWg+LQfvxDIW/fdPkSiuQk7h3frKMYtsQhtIW42wkErDcy9UMVxhGW4O7w== sha1-EGgvYBb9hmclw20izmu70Cm/RUU="}, "registry": "npm", "hash": "10682f6016fd866725c36d22ce6bbbd029bf4545"}