
================================================================
======== Log Rotated - 2/5/2025, 14:12:13                       
================================================================

================================================================
======== FXServer Starting - 2/5/2025, 14:12:24                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 84 resources.
[           resources] 1 warning was encountered.
[38;5;66m[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] [0mStarted resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin][97m Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] [97mCreating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for bln_belt
[           resources] Started resource bln_belt
[    c-scripting-core] [97mCreating script environments for bln_belt_lasso
[           resources] Started resource bln_belt_lasso
[           resources] Warning: Resource cleaner does not support the current game (rdr3).
[ citizen-server-impl] Couldn't start resource cleaner.
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[    c-scripting-core] Creating script environments for jp-mining
[           resources] Started resource jp-mining
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] [0mCreating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[38;5;161m[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] [0mStarted resource rsg-inventory
[    c-scripting-core] [0mCreating script environments for redemrp_butchertable
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] Creating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] [0mCreating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] [0mStarted resource rsg-horses
[    c-scripting-core] [0mCreating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] [0mCreating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[38;5;161m[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[38;5;83m[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] [0mStarted resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[script:rsg-inventory] 2 inventories successfully loaded
[     script:bln_belt] [bln_belt] ❌ Failed to check version. Status: 404
[script:bln_belt_lass] [bln_belt_lasso] ✓ Up to date (v1.0.1)
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/5/2025, 14:17:15                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] [93mWarning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 83 resources.
[           resources] 1 warning was encountered.
[          svadhesive] [0mAuthenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] [0mCreating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] [97mCreating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] [0mCreating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for bln_belt
[           resources] Started resource bln_belt
[           resources] Warning: Resource cleaner does not support the current game (rdr3).
[ citizen-server-impl] Couldn't start resource cleaner.
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[    c-scripting-core] Creating script environments for jp-mining
[           resources] Started resource jp-mining
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] [0mCreating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] [0mStarted resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] [0mCreating script environments for redemrp_butchertable
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] [0mCreating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] [0mStarted resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[38;5;161m[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] [0mStarted resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] [0mStarted resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] [0mCreating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] [0mCreating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] [0mCreating script environments for redm-ipls
[           resources] [0mStarted resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [36m[rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 2 inventories successfully loaded
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[     script:bln_belt] [bln_belt] ❌ Failed to check version. Status: 404
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/5/2025, 14:18:11                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 83 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[38;5;161m[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[38;5;83m[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] [0mCreating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] [97mStarted resource ip-chat
[    c-scripting-core] [0mCreating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] [97mCreating script environments for bln_belt
[           resources] Started resource bln_belt
[           resources] Warning: Resource cleaner does not support the current game (rdr3).
[ citizen-server-impl] Couldn't start resource cleaner.
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] [0mCreating script environments for interact-sound
[           resources] Started resource interact-sound
[    c-scripting-core] Creating script environments for jp-mining
[           resources] Started resource jp-mining
[           resources] Started resource menu_base
[    c-scripting-core] [0mCreating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for redemrp_butchertable
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] Creating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[    c-scripting-core] [0mCreating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] [0mCreating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] [0mStarted resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] [0mCreating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] [0mCreating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] [0mCreating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] [0mCreating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] [0mCreating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] [0mCreating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[38;5;120m[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[ script:rsg-bossmenu] [36m[rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[script:rsg-inventory] 2 inventories successfully loaded
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[     script:bln_belt] [bln_belt] ❌ Failed to check version. Status: 404
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/5/2025, 14:20:23                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 83 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[38;5;161m[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] [97mStarted resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for bln_belt
[           resources] Started resource bln_belt
[           resources] Warning: Resource cleaner does not support the current game (rdr3).
[ citizen-server-impl] Couldn't start resource cleaner.
[    c-scripting-core] [0mCreating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[    c-scripting-core] Creating script environments for jp-mining
[           resources] Started resource jp-mining
[           resources] [0mStarted resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for redemrp_butchertable
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] Creating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] [0mCreating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[38;5;83m[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] [0mStarted resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] [0mCreating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] [0mCreating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[38;5;83m[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[38;5;68m[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [36m[11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 2 inventories successfully loaded
[ citizen-server-impl]         fff                          
[ citizen-server-impl] [91m  cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[     script:bln_belt] [bln_belt] ❌ Failed to check version. Status: 404
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/5/2025, 14:20:35                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 83 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] [0mCreating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[38;5;83m[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] [97mCreating script environments for oxmysql
[           resources] [0mStarted resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] [0mCreating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] [0mCreating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] [0mCreating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] [97mStarted resource PolyZone
[    c-scripting-core] Creating script environments for bln_belt
[           resources] Started resource bln_belt
[           resources] Warning: Resource cleaner does not support the current game (rdr3).
[ citizen-server-impl] Couldn't start resource cleaner.
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] [93mQUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[    c-scripting-core] Creating script environments for jp-mining
[           resources] Started resource jp-mining
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] [0mCreating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[38;5;161m[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] [0mStarted resource redemrp_Deliveryjob
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for redemrp_butchertable
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] [0mCreating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[    c-scripting-core] Creating script environments for weathersync
[           resources] [0mStarted resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] [0mStarted resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] [0mCreating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] [0mCreating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] [0mCreating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] [0mCreating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] [0mCreating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] [0mCreating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] [0mCreating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] [0mCreating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 2 inventories successfully loaded
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[     script:bln_belt] [bln_belt] ❌ Failed to check version. Status: 404
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/5/2025, 14:21:42                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 83 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] [97mCreating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] [0mCreating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for bln_belt
[           resources] Started resource bln_belt
[           resources] Warning: Resource cleaner does not support the current game (rdr3).
[ citizen-server-impl] Couldn't start resource cleaner.
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[    c-scripting-core] Creating script environments for jp-mining
[           resources] Started resource jp-mining
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] [0mCreating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] [0mCreating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for redemrp_butchertable
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] Creating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] [0mCreating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] [0mCreating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] [0mCreating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] [0mCreating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] [0mCreating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] [0mCreating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] [0mCreating script environments for redm-ipls
[           resources] [0mStarted resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 2 inventories successfully loaded
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[     script:bln_belt] [bln_belt] ❌ Failed to check version. Status: 404

================================================================
======== FXServer Starting - 2/5/2025, 14:22:14                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] [0mScanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 83 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] [97mCreating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] [0mCreating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] [97mCreating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] [0mCreating script environments for bln_belt_attachments
[           resources] Started resource bln_belt_attachments
[           resources] Warning: Resource cleaner does not support the current game (rdr3).
[ citizen-server-impl] Couldn't start resource cleaner.[0m
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[    c-scripting-core] Creating script environments for jp-mining
[           resources] Started resource jp-mining
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] [0mCreating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] [0mCreating script environments for redemrp_butchertable
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] Creating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] [0mCreating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[38;5;83m[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] [0mStarted resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] [0mCreating script environments for rsg-essentials
[           resources] [0mStarted resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[38;5;161m[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] [0mCreating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] [0mCreating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 2 inventories successfully loaded
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] [91mcc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[script:bln_belt_atta] [bln_belt_attachments] ✓ Up to date (v1.2.1)
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/5/2025, 14:24:36                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 83 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] [0mServer license key authentication succeeded. Welcome!
[    c-scripting-core] [0mCreating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [36m[txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] [97mStarted resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] [97mCreating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] [0mCreating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[38;5;161m[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] [0mCreating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] [0mStarted resource PolyZone
[    c-scripting-core] Creating script environments for bln_belt_attachments
[           resources] Started resource bln_belt_attachments
[           resources] Warning: Resource cleaner does not support the current game (rdr3).
[ citizen-server-impl] Couldn't start resource cleaner.
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[    c-scripting-core] [0mCreating script environments for jp-mining
[           resources] Started resource jp-mining
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] [0mCreating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for redemrp_butchertable
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] Creating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[    c-scripting-core] [0mCreating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] [0mCreating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] [0mCreating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] [0mCreating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] [0mCreating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] [0mCreating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[script:rsg-inventory] 2 inventories successfully loaded
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [36m[rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[38;5;58m[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] [0mcc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[script:bln_belt_atta] [0m[bln_belt_attachments] ✓ Up to date (v1.2.1)
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/5/2025, 14:28:17                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 82 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] [0mStarted resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] [97mCreating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] [0mCreating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for bln_belt_attachments
[           resources] Started resource bln_belt_attachments
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] [0mQUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[    c-scripting-core] Creating script environments for jp-mining
[           resources] Started resource jp-mining
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] [0mCreating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] [0mCreating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] [0mCreating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for redemrp_butchertable
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] Creating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[38;5;161m[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] [0mCreating script environments for rsg-animations
[           resources] Started resource rsg-animations
[38;5;161m[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] [0mCreating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] [0mCreating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] [0mCreating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] [0mCreating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] [0mCreating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] [0mStarted resource rsg-gangmenu
[    c-scripting-core] [0mCreating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] [0mCreating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] [0mCreating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] [0mCreating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] [0mCreating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] [0m2 inventories successfully loaded
[ citizen-server-impl] [97m        fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] [91mcc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[script:bln_belt_atta] [bln_belt_attachments] ✓ Up to date (v1.2.1)
[ citizen-server-impl] server thread hitch warning: timer interval of 705 milliseconds
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[ citizen-server-impl] [0mserver thread hitch warning: timer interval of 152 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 202 milliseconds
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] is loading into the server
[      script:oxmysql] [11.6.2-MariaDB] rsg-core took 383.5520ms to execute a query!
[      script:oxmysql] SELECT id, reason, expire FROM bans WHERE license = ? ["license:10de59f3e77fd041e517a172faf4b229c64bea90"]
[ citizen-server-impl] server thread hitch warning: timer interval of 162 milliseconds
[ citizen-server-impl] [97mnetwork thread hitch warning: timer interval of 187 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 1064 milliseconds
[ citizen-server-impl] network thread hitch warning: timer interval of 1068 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 1506 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 1929 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 705 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 237 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 386 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 272 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 542 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 807 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 432 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-multicharacter took 414.2485ms to execute a query!
[      script:oxmysql] SELECT * FROM players WHERE license = ? ["license:10de59f3e77fd041e517a172faf4b229c64bea90"]
[ citizen-server-impl] server thread hitch warning: timer interval of 619 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 158 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 251 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 265 milliseconds
[     script:rsg-core] [97m[rsg-core:LOG] Babygang PLAYER SAVED!
[script:rsg-multichar] [rsg-core] Babygang (Citizen ID: ZDL62613) has succesfully loaded!
[ citizen-server-impl] server thread hitch warning: timer interval of 304 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 364 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 156 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 256 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 190 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 639 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 295 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-ammo took 420.7587ms to execute a query!
[      script:oxmysql] SELECT * FROM player_ammo WHERE citizenid = ? LIMIT 1 ["ZDL62613"]
[ citizen-server-impl] server thread hitch warning: timer interval of 218 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-hud took 356.2862ms to execute a query!
[      script:oxmysql] SELECT COUNT(*) FROM telegrams WHERE citizenid = ? AND (status = ? OR birdstatus = ?) ["ZDL62613",0,0]
[ citizen-server-impl] server thread hitch warning: timer interval of 420 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 269 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 499 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 530 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 2138 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-wardrobe took 565.0080ms to execute a query!
[      script:oxmysql] SELECT * FROM playerskins WHERE citizenid = ? ["ZDL62613"]
[ citizen-server-impl] server thread hitch warning: timer interval of 1116 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-ammo took 1864.9778ms to execute a query!
[      script:oxmysql] UPDATE player_ammo SET ammo_pistol_express_explosive = ?, ammo_revolver_high_velocity = ?, ammo_tomahawk = ?, ammo_repeater_express_explosive = ?, ammo_pistol_express = ?, ammo_rifle_express_explosive = ?, ammo_arrow_fire = ?, ammo_poisonbottle = ?, ammo_throwing_knives_drain = ?, ammo_revolver_split_point = ?, ammo_repeater_high_velocity = ?, ammo_molotov = ?, ammo_repeater_split_point = ?, ammo_throwing_knives = ?, ammo_rifle_elephant = ?, ammo_revolver = ?, ammo_shotgun = ?, ammo_bolas_hawkmoth = ?, ammo_dynamite = ?, ammo_shotgun_slug_explosive = ?, ammo_bolas = ?, ammo_pistol = ?, ammo_revolver_express_explosive = ?, ammo_arrow_dynamite = ?, ammo_arrow_small_game = ?, ammo_hatchet = ?, ammo_rifle = ?, ammo_repeater_express = ?, ammo_pistol_high_velocity = ?, ammo_rifle_high_velocity = ?, ammo_bolas_intertwined = ?, ammo_throwing_knives_poison = ?, ammo_rifle_split_point = ?, ammo_hatchet_cleaver = ?, ammo_repeater = ?, ammo_rifle_express = ?, ammo_hatchet_hunter = ?, ammo_shotgun_slug = ?, ammo_arrow = ?, ammo_22 = ?, ammo_bolas_ironspiked = ?, ammo_arrow_poison = ?, ammo_22_tranquilizer = ?, ammo_revolver_express = ?, ammo_shotgun_buckshot_incendiary = ?, ammo_pistol_split_point = ?, ammo_tomahawk_ancient = ? WHERE citizenid = ? [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,17,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,36,0,0,0,0,0,0,0,0,0,0,0,0,"ZDL62613"]
[ citizen-server-impl] server thread hitch warning: timer interval of 835 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 206 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-ammo took 1273.2140ms to execute a query!
[      script:oxmysql] UPDATE player_ammo SET ammo_pistol_express_explosive = ?, ammo_revolver_high_velocity = ?, ammo_tomahawk = ?, ammo_repeater_express_explosive = ?, ammo_pistol_express = ?, ammo_rifle_express_explosive = ?, ammo_arrow_fire = ?, ammo_poisonbottle = ?, ammo_throwing_knives_drain = ?, ammo_revolver_split_point = ?, ammo_repeater_high_velocity = ?, ammo_molotov = ?, ammo_repeater_split_point = ?, ammo_throwing_knives = ?, ammo_rifle_elephant = ?, ammo_revolver = ?, ammo_shotgun = ?, ammo_bolas_hawkmoth = ?, ammo_dynamite = ?, ammo_shotgun_slug_explosive = ?, ammo_bolas = ?, ammo_pistol = ?, ammo_revolver_express_explosive = ?, ammo_arrow_dynamite = ?, ammo_arrow_small_game = ?, ammo_hatchet = ?, ammo_rifle = ?, ammo_repeater_express = ?, ammo_pistol_high_velocity = ?, ammo_rifle_high_velocity = ?, ammo_bolas_intertwined = ?, ammo_throwing_knives_poison = ?, ammo_rifle_split_point = ?, ammo_hatchet_cleaver = ?, ammo_repeater = ?, ammo_rifle_express = ?, ammo_hatchet_hunter = ?, ammo_shotgun_slug = ?, ammo_arrow = ?, ammo_22 = ?, ammo_bolas_ironspiked = ?, ammo_arrow_poison = ?, ammo_22_tranquilizer = ?, ammo_revolver_express = ?, ammo_shotgun_buckshot_incendiary = ?, ammo_pistol_split_point = ?, ammo_tomahawk_ancient = ? WHERE citizenid = ? [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,17,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,36,0,0,0,0,0,0,0,0,0,0,0,0,"ZDL62613"]
[      script:oxmysql] [11.6.2-MariaDB] rsg-hud took 1320.6968ms to execute a query!
[      script:oxmysql] SELECT outlawstatus FROM players WHERE citizenid = ? ["ZDL62613"]
[      script:oxmysql] [11.6.2-MariaDB] [93mrsg-ammo took 1321.3371ms to execute a query!
[      script:oxmysql] UPDATE player_ammo SET ammo_pistol_express_explosive = ?, ammo_revolver_high_velocity = ?, ammo_tomahawk = ?, ammo_repeater_express_explosive = ?, ammo_pistol_express = ?, ammo_rifle_express_explosive = ?, ammo_arrow_fire = ?, ammo_poisonbottle = ?, ammo_throwing_knives_drain = ?, ammo_revolver_split_point = ?, ammo_repeater_high_velocity = ?, ammo_molotov = ?, ammo_repeater_split_point = ?, ammo_throwing_knives = ?, ammo_rifle_elephant = ?, ammo_revolver = ?, ammo_shotgun = ?, ammo_bolas_hawkmoth = ?, ammo_dynamite = ?, ammo_shotgun_slug_explosive = ?, ammo_bolas = ?, ammo_pistol = ?, ammo_revolver_express_explosive = ?, ammo_arrow_dynamite = ?, ammo_arrow_small_game = ?, ammo_hatchet = ?, ammo_rifle = ?, ammo_repeater_express = ?, ammo_pistol_high_velocity = ?, ammo_rifle_high_velocity = ?, ammo_bolas_intertwined = ?, ammo_throwing_knives_poison = ?, ammo_rifle_split_point = ?, ammo_hatchet_cleaver = ?, ammo_repeater = ?, ammo_rifle_express = ?, ammo_hatchet_hunter = ?, ammo_shotgun_slug = ?, ammo_arrow = ?, ammo_22 = ?, ammo_bolas_ironspiked = ?, ammo_arrow_poison = ?, ammo_22_tranquilizer = ?, ammo_revolver_express = ?, ammo_shotgun_buckshot_incendiary = ?, ammo_pistol_split_point = ?, ammo_tomahawk_ancient = ? WHERE citizenid = ? [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,17,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,36,0,0,0,0,0,0,0,0,0,0,0,0,"ZDL62613"]
[ citizen-server-impl] server thread hitch warning: timer interval of 690 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-hud took 1343.0175ms to execute a query!
[      script:oxmysql] SELECT COUNT(*) FROM telegrams WHERE citizenid = ? AND (status = ? OR birdstatus = ?) ["ZDL62613",0,0]
[ citizen-server-impl] server thread hitch warning: timer interval of 274 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 434 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 324 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 541 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 1103 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 268 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 2785 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 153 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 187 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 211 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 152 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 422 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 349 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 170 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 414 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 157 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 205 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 174 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[script:rsg-inventory] AddItem: Invalid item
[script:redemrp_butch] SCRIPT ERROR: @redemrp_butchertable/server.lua:31: attempt to call a nil value (field 'AddXp')
[ citizen-server-impl] server thread hitch warning: timer interval of 165 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[script:rsg-inventory] AddItem: Invalid item
[script:redemrp_butch] SCRIPT ERROR: @redemrp_butchertable/server.lua:31: attempt to call a nil value (field 'AddXp')
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 2273 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 108 milliseconds

================================================================
======== FXServer Starting - 2/5/2025, 16:16:07                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[38;5;83m[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 82 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] [0mServer license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] [97mStarted resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] [0mCreating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] [0mCreating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[38;5;161m[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] [0mCreating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] [97mCreating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for bln_belt_attachments
[           resources] Started resource bln_belt_attachments
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[    c-scripting-core] Creating script environments for jp-mining
[           resources] Started resource jp-mining
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] [0mCreating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] [0mCreating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for redemrp_butchertable
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] Creating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[    c-scripting-core] [0mCreating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] [0mCreating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[38;5;83m[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] [0mCreating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[38;5;161m[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] [0mStarted resource rsg-playerinfo
[    c-scripting-core] [0mCreating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [36m[rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [0m[rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-inventory] 2 inventories successfully loaded
[    script:redm-ipls] [redm-ipls][32m Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[script:bln_belt_atta] [0m[bln_belt_attachments] ✓ Up to date (v1.2.1)
[ script:connectqueue] QUEUE: [97mBabygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[ script:connectqueue] QUEUE: [97mBabygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] is loading into the server
[ citizen-server-impl] server thread hitch warning: timer interval of 204 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[script:rsg-multichar] [rsg-core] Babygang (Citizen ID: ZDL62613) has succesfully loaded!
[ citizen-server-impl] server thread hitch warning: timer interval of 289 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-core took 360.3365ms to execute a query!
[      script:oxmysql] INSERT INTO players (citizenid, cid, license, name, money, charinfo, job, gang, position, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE cid = ?, name = ?, money = ?, charinfo = ?, job = ?, gang = ?, position = ?, metadata = ? ["ZDL62613",1,"license:10de59f3e77fd041e517a172faf4b229c64bea90","Babygang","{\"cash\":97259.4975,\"bank\":6,\"bloodmoney\":0,\"armbank\":0,\"valbank\":0,\"blkbank\":0,\"rhobank\":0}","{\"birthdate\":\"1870-01-01\",\"cid\":\"1\",\"account\":\"US04RSGCore6183584342\",\"gender\":0,\"firstname\":\"SLENTENG\",\"nationality\":\"INDONESIA\",\"lastname\":\"SALOMO\"}","{\"isboss\":false,\"label\":\"Strawberry Law Enforcement\",\"grade\":{\"level\":1,\"isboss\":false,\"name\":\"Deputy\",\"payment\":25},\"onduty\":true,\"type\":\"leo\",\"name\":\"strlaw\",\"payment\":25}","{\"grade\":{\"level\":0,\"isboss\":false,\"name\":\"Unaffiliated\"},\"isboss\":false,\"label\":\"No Gang\",\"name\":\"none\"}","{\"x\":-562.************,\"y\":-3776.**********,\"z\":238.*************}","{\"bloodtype\":\"AB-\",\"injail\":0,\"jailitems\":[],\"hunger\":74.**************,\"health\":122,\"thirst\":77.**************,\"criminalrecord\":{\"hasRecord\":false},\"ishandcuffed\":false,\"isdead\":false,\"stress\":7,\"fingerprint\":\"vz977H61SAo4118\",\"cleanliness\":100.0,\"rep\":[],\"callsign\":\"NO CALLSIGN\",\"armor\":0,\"status\":[],\"walletid\":\"RSG-********\"}",1,"Babygang","{\"cash\":97259.4975,\"bank\":6,\"bloodmoney\":0,\"armbank\":0,\"valbank\":0,\"blkbank\":0,\"rhobank\":0}","{\"birthdate\":\"1870-01-01\",\"cid\":\"1\",\"account\":\"US04RSGCore6183584342\",\"gender\":0,\"firstname\":\"SLENTENG\",\"nationality\":\"INDONESIA\",\"lastname\":\"SALOMO\"}","{\"isboss\":false,\"label\":\"Strawberry Law Enforcement\",\"grade\":{\"level\":1,\"isboss\":false,\"name\":\"Deputy\",\"payment\":25},\"onduty\":true,\"type\":\"leo\",\"name\":\"strlaw\",\"payment\":25}","{\"grade\":{\"level\":0,\"isboss\":false,\"name\":\"Unaffiliated\"},\"isboss\":false,\"label\":\"No Gang\",\"name\":\"none\"}","{\"x\":-562.************,\"y\":-3776.**********,\"z\":238.*************}","{\"bloodtype\":\"AB-\",\"injail\":0,\"jailitems\":[],\"hunger\":74.**************,\"health\":122,\"thirst\":77.**************,\"criminalrecord\":{\"hasRecord\":false},\"ishandcuffed\":false,\"isdead\":false,\"stress\":7,\"fingerprint\":\"vz977H61SAo4118\",\"cleanliness\":100.0,\"rep\":[],\"callsign\":\"NO CALLSIGN\",\"armor\":0,\"status\":[],\"walletid\":\"RSG-********\"}"]
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 391 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-hud took 625.6338ms to execute a query!
[      script:oxmysql] SELECT COUNT(*) FROM telegrams WHERE citizenid = ? AND (status = ? OR birdstatus = ?) ["ZDL62613",0,0]
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
> txaReportResources
[      script:monitor] [txAdmin] Sending resources list to txAdmin.
[ citizen-server-impl] server thread hitch warning: timer interval of 337 milliseconds
> ensure "rsg-radialmenu"
[           resources] Stopping resource rsg-radialmenu
> txaReportResources
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[ citizen-server-impl] server thread hitch warning: timer interval of 2501 milliseconds
[      script:monitor] [txAdmin] Sending resources list to txAdmin.
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 378 milliseconds
