{"manifest": {"name": "@protobufjs/eventemitter", "description": "A minimal event emitter.", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/dcodeIO/protobuf.js.git"}, "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "types": "index.d.ts", "devDependencies": {"istanbul": "^0.4.5", "tape": "^4.6.3"}, "scripts": {"test": "tape tests/*.js", "coverage": "istanbul cover node_modules/tape/bin/tape tests/*.js"}, "_registry": "npm", "_loc": "B:\\txData\\RexshackRedMBuild_9B54B7.base\\cache\\yarn-cache\\v6\\npm-@protobufjs-eventemitter-1.1.0-355cbc98bafad5978f9ed095f397621f1d066b70-integrity\\node_modules\\@protobufjs\\eventemitter\\package.json", "readmeFilename": "README.md", "readme": "@protobufjs/eventemitter\n========================\n[![npm](https://img.shields.io/npm/v/@protobufjs/eventemitter.svg)](https://www.npmjs.com/package/@protobufjs/eventemitter)\n\nA minimal event emitter.\n\nAPI\n---\n\n* **new EventEmitter()**<br />\n  Constructs a new event emitter instance.\n\n* **EventEmitter#on(evt: `string`, fn: `function`, [ctx: `Object`]): `EventEmitter`**<br />\n  Registers an event listener.\n\n* **EventEmitter#off([evt: `string`], [fn: `function`]): `EventEmitter`**<br />\n  Removes an event listener or any matching listeners if arguments are omitted.\n\n* **EventEmitter#emit(evt: `string`, ...args: `*`): `EventEmitter`**<br />\n  Emits an event by calling its listeners with the specified arguments.\n\n**License:** [BSD 3-Clause License](https://opensource.org/licenses/BSD-3-Clause)\n", "licenseText": "Copyright (c) 2016, <PERSON>  All rights reserved.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are\nmet:\n\n* Redistributions of source code must retain the above copyright\n  notice, this list of conditions and the following disclaimer.\n* Redistributions in binary form must reproduce the above copyright\n  notice, this list of conditions and the following disclaimer in the\n  documentation and/or other materials provided with the distribution.\n* Neither the name of its author, nor the names of its contributors\n  may be used to endorse or promote products derived from this software\n  without specific prior written permission.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n\"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\nLIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\nA PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\nOWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\nSPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIA<PERSON> DAMAGES (INCLUDING, BUT NOT\nLIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\nDATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\nTHEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\nOF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz#355cbc98bafad5978f9ed095f397621f1d066b70", "type": "tarball", "reference": "https://registry.yarnpkg.com/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz", "hash": "355cbc98bafad5978f9ed095f397621f1d066b70", "integrity": "sha1-NVy8mLr61ZePntCV85diHx0Ga3A=", "registry": "npm", "packageName": "@protobufjs/eventemitter", "cacheIntegrity": "sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q== sha1-NVy8mLr61ZePntCV85diHx0Ga3A="}, "registry": "npm", "hash": "355cbc98bafad5978f9ed095f397621f1d066b70"}