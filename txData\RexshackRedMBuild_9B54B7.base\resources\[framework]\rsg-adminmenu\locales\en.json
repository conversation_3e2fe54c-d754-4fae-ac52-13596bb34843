{"cl_client_0": "<PERSON><PERSON>", "cl_client_1": "view admin options", "cl_client_2": "Player Options", "cl_client_3": "view player options", "cl_client_88": "Player Finances", "cl_client_89": "adjust player finances", "cl_client_4": "Troll Options", "cl_client_5": "view troll options", "cl_client_6": "Manage Server", "cl_client_7": "view server options", "cl_client_8": "Developer Options", "cl_client_9": "view developer options", "cl_client_10": "Admin Options Menu", "cl_client_11": "Teleport to <PERSON><PERSON>", "cl_client_12": "you must have a marker set before doing this", "cl_client_13": "Self Revive", "cl_client_14": "revive yourself from the dead", "cl_client_15": "Go Invisible", "cl_client_16": "toggle invisible on/off", "cl_client_146": "Toggle IDs", "cl_client_147": "Toggle Player IDs on/off", "cl_client_17": "God Mode", "cl_client_18": "toggle god mode on/off", "cl_client_19": "ID:", "cl_client_21": "Players Menu", "cl_client_137": "Player Info", "cl_client_138": "get players information", "cl_client_22": "Revive Player", "cl_client_23": "revive this player", "cl_client_130": "Give Item", "cl_client_131": "given an item to a player", "cl_client_24": "Player Inventory", "cl_client_25": "open a players inventory, press [I] when open", "cl_client_26": "Kick Player", "cl_client_27": "kick a player from the server with reason", "cl_client_28": "Ban Player", "cl_client_29": "ban a player from the server with reason", "cl_client_30": "GoTo Player", "cl_client_31": "goto a player", "cl_client_32": "Bring Player", "cl_client_33": "bring a player to you", "cl_client_34": "Toggle Freeze Player", "cl_client_35": "toggles freeze player on/off", "cl_client_36": "Toggle Spectate Player", "cl_client_37": "toggles spectate on another player on/off", "cl_client_38": "Server Options Menu", "cl_client_39": "Server Weather", "cl_client_40": "adjust server weather", "cl_adminmenu": "Admin Options", "cl_adminmenu_a": "toggled > INVISIBLE MODE <", "cl_client_42": "Invisible On", "cl_client_43": "as you can see you are invisible!", "cl_client_44": "Invisible Off", "cl_client_45": "as you can see you are not invisible!", "cl_client_46": "God Mode On", "cl_client_47": "god mode is now on!", "cl_adminmenu_b": "toggled > GODMODE <", "cl_client_48": "God Mode Off", "cl_client_49": "god mode is now off!", "cl_client_50": "Kick Player", "cl_client_51": "Reason", "cl_client_52": "Ban Player", "cl_client_53": "Ban Type", "cl_client_53_a": "Permanent", "cl_client_53_b": "Temporary", "cl_client_54": "Ban Time", "cl_client_55": "1 Hour", "cl_client_56": "6 Hours", "cl_client_57": "12 Hours", "cl_client_58": "1 Day", "cl_client_59": "3 Days", "cl_client_60": "1 Week", "cl_client_61": "1 Month", "cl_client_62": "3 Months", "cl_client_63": "6 Months", "cl_client_64": "1 Year", "cl_client_65": "Permanent", "cl_client_66": "Player Banned", "cl_client_67": " has been banned permanently", "cl_client_68": " has a temporary ban set", "cl_client_132": "Give Item to Player", "cl_client_133": "Inventory Item", "cl_client_134": "Amount", "cl_client_139": "Admin Player Info", "cl_client_140": "Name", "cl_client_141": "Job", "cl_client_142": "Job Grade", "cl_client_143": "Cash", "cl_client_144": "Blood Money", "cl_client_153": "Bank", "cl_client_148": "ValBank", "cl_client_149": "RhoBank", "cl_client_150": "BlkBank", "cl_client_151": "ArmBank", "cl_anim_test": "Animation Tester", "cl_anim_dictionary": "animDictionary", "cl_anim_name": "animationName", "cl_anim_flag": "flag", "cl_anim_length": "length : in milliseconds", "cl_coords_menu": "<PERSON><PERSON>", "cl_coords_70": "Copy Vector 2", "cl_coords_71": "copy vector2 coords", "cl_coords_72": "Copy Vector 3", "cl_coords_73": "copy vector3 coords", "cl_coords_74": "Copy Vector 4", "cl_coords_75": "copy vector4 coords", "cl_coords_76": "<PERSON><PERSON>ing", "cl_coords_77": "copy heading", "cl_coords_print_list": "Print List On", "cl_coords_print_list_a": "Enable printing of coordinates to a list", "cl_coords_print_full": "Print List Full", "cl_coords_print_full_a": "Print the full list of coordinates", "cl_coords_78": "<PERSON><PERSON><PERSON> Copied", "cl_coords_79": "vector2 coords copied to the clipboard", "cl_coords_80": "vector3 coords copied to the clipboard", "cl_coords_81": "vector4 coords copied to the clipboard", "cl_coords_82": "Heading Copied", "cl_coords_83": "heading copied to the clipboard", "cl_coords_printlist": "Print List", "cl_coords_printlist_a": "List printing enabled. Coordinates will be added to the list.", "cl_coords_printlist_b": "List copied to clipboard", "cl_coords_printlist_c": "The list is empty", "cl_dev_menu": "Develo<PERSON> Menu", "cl_dev_menu_a": "Spawn Admin Horse", "cl_dev_menu_b": "spawn a admin horse", "cl_dev_menu_c": "<PERSON><PERSON>", "cl_dev_menu_d": "copy coords to clipboard", "cl_dev_menu_e": "Animation Tester", "cl_dev_menu_f": "test animations", "cl_dev_menu_g": "<PERSON><PERSON><PERSON>", "cl_dev_menu_h": "get entity hash", "cl_dev_menu_i": "Toggle Door IDs on/off", "cl_dev_menu_j": "used to get door ids", "cl_dev_menu_l": "<PERSON><PERSON>", "cl_dev_menu_m": "used to spawn npc/animals", "cl_dev_126": "Spawn Admin Horse", "cl_dev_127": "Get Entity Hash", "cl_dev_128": "entity name", "cl_dev_129": "example : PROVISION_ALLIGATOR_SKIN", "cl_dev_copy": "<PERSON><PERSON><PERSON>d", "cl_dev_copy_a": "entity hash of", "cl_dev_copy_b": "has been copied to your clipboard", "cl_dev_spawnped": "Spawn Ped/Animal", "cl_dev_spawnped1": "ped name", "cl_dev_spawnped2": "example : mp_a_c_wolf_01", "cl_dev_spawnped3": "outfit", "cl_dev_spawnped4": "outfit number for ped/animal", "cl_dev_spawnped5": "distance", "cl_dev_spawnped6": "spawn distrance away from you", "cl_dev_spawnped7": "freeze", "cl_dev_spawnped8": "freeze npc/animal on spawn", "cl_dev_spawnped9": "True", "cl_dev_spawnped10": "False", "cl_dev_spawnped11": "spawn dead", "cl_dev_spawnped12": "spawn npc/animal dead", "cl_door_on": "Door IDs On", "cl_door_off": "Door IDs Off", "cl_door_id": "Door ID", "cl_door_o": "Object", "cl_finan_menu": "Player Finances", "cl_finan_19": "ID:", "cl_finan_90": "Finances Options Menu", "cl_finan_122": "Bank : $", "cl_finan_122_a": "ValBank : $", "cl_finan_122_b": "RhoBank : $", "cl_finan_122_c": "BlkBank : $", "cl_finan_122_d": "ArmBank : $", "cl_finan_123": "Cash : $", "cl_finan_123_a": "Blood money : $", "cl_finan_119": "current players bank ballance", "cl_finan_120": "current players cash ballance", "cl_finan_121": "current players blood money ballance", "cl_finan_91": "Give Money", "cl_finan_92": "give money to player", "cl_finan_93": "Remove Money", "cl_finan_94": "remove money from player", "cl_finan_95": "Type", "cl_finan_96": "chose the type to give to the player", "cl_finan_2": "Std Bank", "cl_finan_2a": "Val Bank", "cl_finan_2b": "Rho Bank", "cl_finan_2c": "Blk Bank", "cl_finan_2d": "Arm Bank", "cl_finan_3": "Cash", "cl_finan_3a": "Blood money", "cl_finan_97": "Amount", "cl_finan_98": "how much do you want to give?", "cl_finan_99": "chose the type to remove from the player", "cl_finan_115": "how much do you want to remove?", "cl_troll_19": "ID", "cl_troll_84": "Troll Player", "cl_troll_85": "Troll Options Menu", "cl_troll_86": "Wild Attack", "cl_troll_87": "troll player by activating a wild attack", "cl_troll_128": "Set Player on Fire", "cl_troll_129": "set a player on fire", "sv_a": "Player Banned", "sv_b": "%s was banned by %s for %s", "sv_c": "system banned you for inappropriate use", "sv_105": "You were permanently banned by the server for: Exploiting", "sv_100": "open the admin menu (Admin Only)", "sv_d": "Unuthorised use of <PERSON><PERSON>", "sv_e": "with citizen id of", "sv_f": "attempted to use the admin menu", "sv_101": "Not Allowed", "sv_102": "you are not allowed to do that!", "sv_g": "Unuthorised Use", "sv_h": "with citizen id of", "sv_i": "banned for using admin revive", "sv_j": "banned for using open inventory", "sv_kicked": "Player Kicked", "sv_kicked_a": "%s was kicked by %s for %s", "sv_kicked_b": "banned for using kick player", "sv_ban": "ANNOUNCEMENT", "sv_ban_a": "Player Banned", "sv_ban_b": "has been banned", "sv_ban_c": "banned for using go to player", "sv_ban_d": "banned for using bring player", "sv_ban_e": "banned for using freeze player", "sv_ban_f": "banned for using spectate player", "sv_ban_g": "banned for using wild attack", "sv_ban_h": "banned for using set on fire", "sv_ban_i": "banned for using give item", "sv_ban_j": "banned for using get player info", "sv_103": "You have been kicked from the server", "sv_104": "🔸 Check our Discord for more information: ", "sv_106": "You have been banned:", "sv_107": "Your ban is permanent.", "sv_108": "🔸 Check our Discord for more information: ", "sv_109": "Ban expires: ", "sv_110": "🔸 Check our Discord for more information: ", "sv_111": "Freeze Player On", "sv_112": "you freezed player ", "sv_113": "Freeze Player Off", "sv_114": "you unfreezed player ", "sv_135": "<PERSON><PERSON>", "sv_136": "item successfully sent", "sv_finan_116": "Error", "sv_finan_117": "player does not have enough ", "sv_finan_118": " to remove!"}