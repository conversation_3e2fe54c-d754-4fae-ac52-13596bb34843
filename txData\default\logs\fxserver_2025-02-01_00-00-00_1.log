
================================================================
======== Log Rotated - 2/1/2025, 10:59:59                       
================================================================

================================================================
======== FXServer Starting - 2/1/2025, 11:00:11                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[           resources] Found 74 resources.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[38;5;161m[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] [0mCreating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] [0mCreating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] [0mStarted resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[     script:rsg-core] SCRIPT ERROR: @rsg-core/server/functions.lua:394: attempt to perform arithmetic on a nil value (field 'PayCheckTimeOut')
[     script:rsg-core] > PaycheckInterval (@rsg-core/server/functions.lua:394)
[     script:rsg-core] > fn (@rsg-core/server/player.lua:645)
[           resources] Started resource rsg-core
[38;5;161m[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[38;5;161m[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] [0mCreating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] [0mCreating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] [0mStarted resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] [0mStarted resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[ script:rsg-multijob] Error parsing script @rsg-multijob/server/server.lua in resource rsg-multijob: @rsg-multijob/server/server.lua:142: <eof> expected near 'end'
[    c-scripting-core] Failed to load script server/server.lua.
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] [0mCreating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[38;5;200m[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 1 inventories successfully loaded
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[       script:ox_lib] An update is available for ox_lib (current version: 3.28.1)

[       script:ox_lib] https://github.com/overextended/ox_lib/releases/tag/v3.29.0
[ citizen-server-impl]         fff                          
[38;5;73m[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/1/2025, 11:02:46                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[           resources] Found 74 resources.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] [0mCreating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] [0mCreating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[     script:rsg-core] SCRIPT ERROR: @rsg-core/server/functions.lua:394: attempt to perform arithmetic on a nil value (field 'PayCheckTimeOut')
[     script:rsg-core] > PaycheckInterval (@rsg-core/server/functions.lua:394)
[     script:rsg-core] > fn (@rsg-core/server/player.lua:645)
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[38;5;83m[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[38;5;161m[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[38;5;161m[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[38;5;161m[    c-scripting-core] Creating script environments for rsg-multijob
[ script:rsg-multijob] Error parsing script @rsg-multijob/server/server.lua in resource rsg-multijob: @rsg-multijob/server/server.lua:142: <eof> expected near 'end'
[    c-scripting-core] Failed to load script server/server.lua.
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] [0mCreating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 1 inventories successfully loaded
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[       script:ox_lib] An update is available for ox_lib (current version: 3.28.1)

[       script:ox_lib] https://github.com/overextended/ox_lib/releases/tag/v3.29.0
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/1/2025, 11:04:56                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[           resources] Found 74 resources.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] [0mCreating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] [0mCreating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[     script:rsg-core] SCRIPT ERROR: @rsg-core/server/functions.lua:394: attempt to perform arithmetic on a nil value (field 'PayCheckTimeOut')
[     script:rsg-core] > PaycheckInterval (@rsg-core/server/functions.lua:394)
[     script:rsg-core] > fn (@rsg-core/server/player.lua:645)
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] [0mCreating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] [0mCreating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[38;5;83m[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[38;5;161m[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] [0mCreating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] [0mCreating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] [0mStarted resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] [0mCreating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[ script:rsg-multijob] Error parsing script @rsg-multijob/server/server.lua in resource rsg-multijob: @rsg-multijob/server/server.lua:142: <eof> expected near 'end'
[    c-scripting-core] Failed to load script server/server.lua.
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[38;5;161m[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 1 inventories successfully loaded
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[38;5;73m[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/1/2025, 11:05:55                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[           resources] Found 74 resources.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] [0mCreating script environments for monitor
[      script:monitor] [txAdmin][97m Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[38;5;161m[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[38;5;161m[    c-scripting-core] Creating script environments for rsg-core
[     script:rsg-core] SCRIPT ERROR: @rsg-core/server/functions.lua:394: attempt to perform arithmetic on a nil value (field 'PayCheckTimeOut')
[     script:rsg-core] > PaycheckInterval (@rsg-core/server/functions.lua:394)
[     script:rsg-core] > fn (@rsg-core/server/player.lua:645)
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] [0mCreating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] [0mStarted resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] [0mCreating script environments for interact-sound
[38;5;83m[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] [0mCreating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] [0mStarted resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[38;5;161m[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] [0mCreating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] [0mCreating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] [0mStarted resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] [0mCreating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] [0mCreating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[ script:rsg-multijob] Error parsing script @rsg-multijob/server/server.lua in resource rsg-multijob: @rsg-multijob/server/server.lua:206: <eof> expected near 'end'
[    c-scripting-core] Failed to load script server/server.lua.
[           resources] [0mStarted resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] [0mCreating script environments for rsg-playerinfo
[           resources] [0mStarted resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 1 inventories successfully loaded
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/1/2025, 11:11:40                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[           resources] Found 74 resources.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] [97mCreating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[     script:rsg-core] SCRIPT ERROR: @rsg-core/server/functions.lua:394: attempt to perform arithmetic on a nil value (field 'PayCheckTimeOut')
[     script:rsg-core] > PaycheckInterval (@rsg-core/server/functions.lua:394)
[     script:rsg-core] > fn (@rsg-core/server/player.lua:645)
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] [0mStarted resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] [0mCreating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] [0mStarted resource menu_base
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] [0mCreating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] [0mCreating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] [0mStarted resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] [0mCreating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] [0mCreating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] [0mCreating script environments for rsg-radialmenu
[           resources] [0mStarted resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[script:rsg-inventory] 1 inventories successfully loaded
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram][32m You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu][32m Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/1/2025, 11:12:18                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[           resources] Found 74 resources.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] [97mCreating script environments for rsg-core
[     script:rsg-core] SCRIPT ERROR: @rsg-core/server/functions.lua:394: attempt to perform arithmetic on a nil value (field 'PayCheckTimeOut')
[     script:rsg-core] > PaycheckInterval (@rsg-core/server/functions.lua:394)
[     script:rsg-core] > fn (@rsg-core/server/player.lua:645)
[           resources] [0mStarted resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for connectqueue
[38;5;173m[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] [0mCreating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] [0mCreating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 1 inventories successfully loaded
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [36m[txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/1/2025, 11:20:21                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[           resources] Found 74 resources.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] [0mCreating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] [97mCreating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] [97mCreating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap [0m
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] [0mStarted resource xsound
[    c-scripting-core] [0mCreating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] [0mCreating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] [0mStarted resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] [0mCreating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] [0mCreating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] [0mStarted resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] [0mCreating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[38;5;35m[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[script:rsg-inventory] 1 inventories successfully loaded
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic][32m Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[ citizen-server-impl] server thread hitch warning: timer interval of 1079 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 321 milliseconds
[ script:connectqueue] SCRIPT ERROR: @connectqueue/shared/sh_queue.lua:492: attempt to concatenate a nil value (local 'name')
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.
[ citizen-server-impl] server thread hitch warning: timer interval of 338 milliseconds

================================================================
======== FXServer Starting - 2/1/2025, 12:41:51                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[           resources] Found 74 resources.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] [0mCreating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] [97mCreating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] [97mStarted resource rsg-core
[    c-scripting-core] [97mCreating script environments for rsg-multicharacter
[           resources] [97mStarted resource rsg-multicharacter
[    c-scripting-core] [97mCreating script environments for rsg-spawn
[38;5;83m[           resources] Started resource rsg-spawn
[    c-scripting-core] [97mCreating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] [0mCreating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] [0mCreating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] [0mCreating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] [0mCreating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] [0mCreating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] [0mCreating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] [0mStarted resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] [0mStarted resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] [0mStarted resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] [0mCreating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[38;5;161m[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [36m[rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.[0m
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [0m[rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[   script:rsg-lawman] [36m[rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [36m[rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[ citizen-server-impl] server thread hitch warning: timer interval of 294 milliseconds
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[ citizen-server-impl] server thread hitch warning: timer interval of 168 milliseconds
[script:rsg-inventory] 1 inventories successfully loaded
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] is loading into the server
[ citizen-server-impl] sync thread hitch warning: timer interval of 315 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 203 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[script:rsg-multichar] [rsg-core] Babygang (Citizen ID: ZDL62613) has succesfully loaded!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[      script:oxmysql] [11.6.2-MariaDB] rsg-hud took 381.6043ms to execute a query!
[      script:oxmysql] SELECT COUNT(*) FROM telegrams WHERE citizenid = ? AND (status = ? OR birdstatus = ?) ["ZDL62613",0,0]
[ citizen-server-impl] sync thread hitch warning: timer interval of 103 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!

================================================================
======== FXServer Starting - 2/1/2025, 14:01:01                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[           resources] Found 77 resources.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] [0mCreating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [36m[txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] [97mCreating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] [97mCreating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] [97mCreating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] [0mCreating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-resources-core] Could not find dependency redemrp_inventory for resource redemrp_butchertable.
[ citizen-server-impl] Couldn't start resource redemrp_butchertable.
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] [0mStarted resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[38;5;161m[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] [0mCreating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] [0mCreating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[38;5;161m[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[script:rsg-inventory] 1 inventories successfully loaded
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[38;5;73m[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.

================================================================
======== FXServer Starting - 2/1/2025, 14:02:43                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[           resources] Found 77 resources.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] [0mStarted resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[38;5;161m[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[38;5;161m[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] [0mStarted resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-resources-core] Could not find dependency redemrp_inventory for resource redemrp_butchertable.
[ citizen-server-impl] Couldn't start resource redemrp_butchertable.
[    c-scripting-core] [0mCreating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] [0mCreating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] [0mCreating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] [0mStarted resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] [0mCreating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[38;5;83m[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] [0mCreating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] [0mCreating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 1 inventories successfully loaded
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
> txaReportResources
[      script:monitor] [txAdmin] Sending resources list to txAdmin.
> ensure "redemrp_butchertable"
[    c-resources-core] Could not find dependency redemrp_inventory for resource redemrp_butchertable.
[ citizen-server-impl] Couldn't start resource redemrp_butchertable.
> txaReportResources
[      script:monitor] [txAdmin] Sending resources list to txAdmin.
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] is loading into the server
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[ citizen-server-impl] sync thread hitch warning: timer interval of 123 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 362 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 256 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 159 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[script:rsg-multichar] [rsg-core] Babygang (Citizen ID: ZDL62613) has succesfully loaded!
[ citizen-server-impl] server thread hitch warning: timer interval of 189 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 189 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 278 milliseconds
[ citizen-server-impl] network thread hitch warning: timer interval of 184 milliseconds
[     script:rsg-core] [0m[rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 2523 milliseconds
