
================================================================
======== FXServer Starting - 1/30/2025, 17:43:45                
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[           resources] Found 74 resources.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[ citizen-server-impl] Running build tasks on resource sessionmanager-rdr3 - it'll restart once completed.
[ citizen-server-impl] Couldn't start resource sessionmanager-rdr3.
[ citizen-server-impl] [0mRunning build tasks on resource monitor - it'll restart once completed.
[         script:yarn] yarn is currently busy: we are waiting to compile monitor
[ citizen-server-impl] Couldn't start resource monitor.
[                 cmd] [0mArgument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[ citizen-server-impl] [0mRunning build tasks on resource sessionmanager-rdr3 - it'll restart once completed.
[ citizen-server-impl] Couldn't start resource sessionmanager-rdr3.
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] [0mCreating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] [0mCreating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] [0mCreating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] [0mCreating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] [0mCreating script environments for rsg-wardrobe
[           resources] [0mStarted resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] [0mCreating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] [0mCreating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] [0mStarted resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] [0mStarted resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] [0mCreating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] [0mStarted resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[         script:yarn] [yarn]	yarn install v1.22.5
[         script:yarn] [1/4] Resolving packages...
[         script:yarn] [2/4] Fetching packages...
[         script:yarn] [3/4] Linking dependencies...
[         script:yarn] [4/4] Building fresh packages...
[         script:yarn] Done in 4.39s.
[         script:yarn] [yarn]	yarn install v1.22.5
[         script:yarn] [1/4] Resolving packages...
[         script:yarn] success Already up-to-date.
[         script:yarn] Done in 3.06s.
[         script:yarn] Error: [yarn]	warning Waiting for the other yarn instance to finish (pid undefined, inside undefined)
[         script:yarn] Error: [yarn]	warning Ignored scripts due to flag.
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [0m[rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [36m[rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[ citizen-server-impl] Build tasks completed - starting resource sessionmanager-rdr3.
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[ citizen-server-impl] Build tasks completed - starting resource sessionmanager-rdr3.
[ citizen-server-impl] server thread hitch warning: timer interval of 980 milliseconds
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[         script:yarn] [yarn]	yarn install v1.22.5
[         script:yarn] info No lockfile found.
[         script:yarn] [1/4] Resolving packages...
[         script:yarn] [2/4] Fetching packages...
[         script:yarn] [3/4] Linking dependencies...
[         script:yarn] [4/4] Building fresh packages...
[         script:yarn] success Saved lockfile.
[         script:yarn] Done in 0.06s.
[         script:yarn] Error: [yarn]	warning Ignored scripts due to flag.
[ citizen-server-impl] Build tasks completed - starting resource monitor.
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] [0mStarted resource monitor
[ citizen-server-impl] server thread hitch warning: timer interval of 431 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[ citizen-server-impl] [97m        fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[38;5;73m[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] is loading into the server
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[ citizen-server-impl] server thread hitch warning: timer interval of 172 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 211 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-multicharacter:LOG] Babygang has succesfully loaded!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG][0m Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 492 milliseconds
[      script:monitor] [txAdmin] HeartBeat failed with code 0 and message: nil
[ citizen-server-impl] server thread hitch warning: timer interval of 2043 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 220 milliseconds
> txaReportResources
[      script:monitor] [txAdmin] Sending resources list to txAdmin.
> txaReportResources
[      script:monitor] [txAdmin] Sending resources list to txAdmin.
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] network thread hitch warning: timer interval of 167 milliseconds
[     script:rsg-core] [rsg-core:LOG][0m Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 2171 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 168 milliseconds
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 1/30/2025, 18:22:05                
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[           resources] Found 74 resources.
[38;5;66m[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] [0mCreating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] [97mCreating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] [97mCreating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] [0mCreating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] [97mCreating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[38;5;83m[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] [0mCreating script environments for weathersync
[           resources] [0mStarted resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[38;5;161m[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] [0mCreating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] [0mStarted resource rsg-barbers
[    c-scripting-core] [0mCreating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[38;5;161m[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] [0mCreating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] [0mCreating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] [0mCreating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] [0mCreating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] [0mCreating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] [0mCreating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[ citizen-server-impl] server thread hitch warning: timer interval of 195 milliseconds
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] [32mDatabase server connection established!
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/

================================================================
======== FXServer Starting - 1/30/2025, 18:45:44                
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[           resources] Found 74 resources.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[                 cmd] [97mArgument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] [0mStarted resource ox_doorlock
[    c-scripting-core] [0mCreating script environments for pma-voice
[           resources] [0mStarted resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[38;5;161m[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] [0mCreating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] [0mCreating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] [0mCreating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] [0mCreating script environments for rsg-horses
[           resources] [0mStarted resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] [0mStarted resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[38;5;161m[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[38;5;83m[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[38;5;197m[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[ citizen-server-impl] [0mserver thread hitch warning: timer interval of 181 milliseconds
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[ script:connectqueue] [0mQUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] is loading into the server
[ citizen-server-impl] server thread hitch warning: timer interval of 236 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 335 milliseconds
[     script:rsg-core] [rsg-core:LOG][0m Babygang PLAYER SAVED!
[script:rsg-multichar] [rsg-core] Babygang (Citizen ID: ZDL62613) has succesfully loaded!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 399 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 213 milliseconds

================================================================
======== FXServer Starting - 1/30/2025, 20:23:12                
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[           resources] Found 74 resources.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] [97mCreating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] [97mStarted resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] [97mStarted resource ox_target
[    c-scripting-core] [97mCreating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] [0mCreating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] [0mStarted resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] [0mCreating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] [0mCreating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] [0mStarted resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] [0mCreating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] [0mCreating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] [0mCreating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] [0mCreating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[      script:oxmysql] [36m[11.6.2-MariaDB] Database server connection established!
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[38;5;73m[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.
[ citizen-server-impl] server thread hitch warning: timer interval of 634 milliseconds

================================================================
======== FXServer Starting - 1/30/2025, 20:29:25                
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[           resources] Found 74 resources.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] [97mStarted resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] [0mStarted resource rsg-menubase
[    c-scripting-core] [97mCreating script environments for ox_target
[           resources] Started resource ox_target
[38;5;161m[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE: [97m [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] [0mCreating script environments for ox_doorlock
[           resources] [0mStarted resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] [0mCreating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] [0mStarted resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] [0mCreating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [0m[rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[ citizen-server-impl] server thread hitch warning: timer interval of 626 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 225 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 530 milliseconds
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[      script:oxmysql] [11.6.2-MariaDB] rsg-core took 335.1318ms to execute a query!
[      script:oxmysql] SELECT id, reason, expire FROM bans WHERE license = ? ["license:10de59f3e77fd041e517a172faf4b229c64bea90"]
[ citizen-server-impl] server thread hitch warning: timer interval of 184 milliseconds
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] is loading into the server
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was removed from the connecting queue because they timed out
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] is loading into the server
[ citizen-server-impl] server thread hitch warning: timer interval of 661 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 263 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 185 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 511 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 264 milliseconds
[ citizen-server-impl] [0mserver thread hitch warning: timer interval of 417 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 249 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 215 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-multicharacter took 362.2106ms to execute a query!
[      script:oxmysql] SELECT * FROM players WHERE license = ? ["license:10de59f3e77fd041e517a172faf4b229c64bea90"]
[ citizen-server-impl] [97mserver thread hitch warning: timer interval of 423 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 196 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 194 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[script:rsg-multichar] [rsg-core] Babygang (Citizen ID: ZDL62613) has succesfully loaded!
[ citizen-server-impl] server thread hitch warning: timer interval of 605 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-core took 324.5510ms to execute a query!
[      script:oxmysql] INSERT INTO players (citizenid, cid, license, name, money, charinfo, job, gang, position, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE cid = ?, name = ?, money = ?, charinfo = ?, job = ?, gang = ?, position = ?, metadata = ? ["ZDL62613",1,"license:10de59f3e77fd041e517a172faf4b229c64bea90","Babygang","{\"armbank\":0,\"valbank\":0,\"bloodmoney\":0,\"cash\":99497.0,\"blkbank\":0,\"rhobank\":0,\"bank\":3}","{\"lastname\":\"SALOMO\",\"nationality\":\"INDONESIA\",\"gender\":0,\"cid\":\"1\",\"account\":\"US04RSGCore6183584342\",\"firstname\":\"SLENTENG\",\"birthdate\":\"1870-01-01\"}","{\"label\":\"Civilian\",\"grade\":{\"isboss\":false,\"name\":\"Freelancer\",\"level\":0},\"payment\":3,\"type\":\"none\",\"onduty\":true,\"name\":\"unemployed\",\"isboss\":false}","{\"name\":\"none\",\"label\":\"No Gang\",\"grade\":{\"isboss\":false,\"name\":\"Unaffiliated\",\"level\":0},\"isboss\":false}","{\"x\":-562.************,\"y\":-3776.**********,\"z\":238.*************}","{\"status\":[],\"rep\":[],\"walletid\":\"RSG-********\",\"hunger\":91.6,\"criminalrecord\":{\"hasRecord\":false},\"cleanliness\":100.0,\"callsign\":\"NO CALLSIGN\",\"isdead\":false,\"ishandcuffed\":false,\"thirst\":96.2,\"armor\":0,\"health\":600,\"injail\":0,\"stress\":4,\"bloodtype\":\"AB-\",\"jailitems\":[],\"fingerprint\":\"vz977H61SAo4118\"}",1,"Babygang","{\"armbank\":0,\"valbank\":0,\"bloodmoney\":0,\"cash\":99497.0,\"blkbank\":0,\"rhobank\":0,\"bank\":3}","{\"lastname\":\"SALOMO\",\"nationality\":\"INDONESIA\",\"gender\":0,\"cid\":\"1\",\"account\":\"US04RSGCore6183584342\",\"firstname\":\"SLENTENG\",\"birthdate\":\"1870-01-01\"}","{\"label\":\"Civilian\",\"grade\":{\"isboss\":false,\"name\":\"Freelancer\",\"level\":0},\"payment\":3,\"type\":\"none\",\"onduty\":true,\"name\":\"unemployed\",\"isboss\":false}","{\"name\":\"none\",\"label\":\"No Gang\",\"grade\":{\"isboss\":false,\"name\":\"Unaffiliated\",\"level\":0},\"isboss\":false}","{\"x\":-562.************,\"y\":-3776.**********,\"z\":238.*************}","{\"status\":[],\"rep\":[],\"walletid\":\"RSG-********\",\"hunger\":91.6,\"criminalrecord\":{\"hasRecord\":false},\"cleanliness\":100.0,\"callsign\":\"NO CALLSIGN\",\"isdead\":false,\"ishandcuffed\":false,\"thirst\":96.2,\"armor\":0,\"health\":600,\"injail\":0,\"stress\":4,\"bloodtype\":\"AB-\",\"jailitems\":[],\"fingerprint\":\"vz977H61SAo4118\"}"]
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] sync thread hitch warning: timer interval of 174 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 735 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-hud took 467.6116ms to execute a query!
[      script:oxmysql] SELECT outlawstatus FROM players WHERE citizenid = ? ["ZDL62613"]
[ citizen-server-impl] server thread hitch warning: timer interval of 188 milliseconds
[      script:oxmysql] [36m[11.6.2-MariaDB] rsg-ammo took 398.6666ms to execute a query!
[      script:oxmysql] UPDATE player_ammo SET ammo_arrow = ?, ammo_hatchet = ?, ammo_rifle_split_point = ?, ammo_poisonbottle = ?, ammo_repeater_express = ?, ammo_hatchet_hunter = ?, ammo_rifle = ?, ammo_revolver_express = ?, ammo_repeater_express_explosive = ?, ammo_repeater = ?, ammo_shotgun_buckshot_incendiary = ?, ammo_molotov = ?, ammo_bolas_hawkmoth = ?, ammo_shotgun_slug_explosive = ?, ammo_revolver_high_velocity = ?, ammo_22 = ?, ammo_repeater_high_velocity = ?, ammo_shotgun_slug = ?, ammo_22_tranquilizer = ?, ammo_rifle_elephant = ?, ammo_throwing_knives = ?, ammo_revolver_express_explosive = ?, ammo_dynamite = ?, ammo_shotgun = ?, ammo_bolas_intertwined = ?, ammo_tomahawk_ancient = ?, ammo_pistol_split_point = ?, ammo_hatchet_cleaver = ?, ammo_pistol_express = ?, ammo_arrow_fire = ?, ammo_arrow_small_game = ?, ammo_pistol_express_explosive = ?, ammo_revolver = ?, ammo_arrow_dynamite = ?, ammo_rifle_express_explosive = ?, ammo_throwing_knives_drain = ?, ammo_repeater_split_point = ?, ammo_bolas_ironspiked = ?, ammo_pistol_high_velocity = ?, ammo_revolver_split_point = ?, ammo_bolas = ?, ammo_rifle_high_velocity = ?, ammo_throwing_knives_poison = ?, ammo_rifle_express = ?, ammo_pistol = ?, ammo_arrow_poison = ?, ammo_tomahawk = ? WHERE citizenid = ? [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,"ZDL62613"]
[ citizen-server-impl] server thread hitch warning: timer interval of 223 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-hud took 891.8351ms to execute a query!
[      script:oxmysql] SELECT COUNT(*) FROM telegrams WHERE citizenid = ? AND (status = ? OR birdstatus = ?) ["ZDL62613",0,0]
[ citizen-server-impl] server thread hitch warning: timer interval of 427 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 513 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 333 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 165 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 216 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 362 milliseconds
[38;5;109m[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 201 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 277 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 2288 milliseconds
