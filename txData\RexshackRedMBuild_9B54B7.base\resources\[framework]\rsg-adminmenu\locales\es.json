{"cl_client_0": "<PERSON><PERSON>", "cl_client_1": "ver opciones de admin", "cl_client_2": "Opciones de Jugador", "cl_client_3": "ver opciones de jugador", "cl_client_88": "Finanzas del Jugador", "cl_client_89": "ajustar finanzas del jugador", "cl_client_4": "Opciones de Trolleo", "cl_client_5": "ver opciones de trolleo", "cl_client_6": "<PERSON><PERSON><PERSON><PERSON>", "cl_client_7": "ver opciones del servidor", "cl_client_8": "Opciones de Desarrollador", "cl_client_9": "ver opciones de desarrollador", "cl_client_10": "Menú de Opciones de Admin", "cl_client_11": "Teletransportarse a Marcador", "cl_client_12": "debes tener un marcador antes de hacer esto", "cl_client_13": "Auto Revivir", "cl_client_14": "revivirte de la muerte", "cl_client_15": "Volverse Invisible", "cl_client_16": "alternar invisible encendido/apagado", "cl_client_146": "Alternar IDs", "cl_client_147": "Alternar IDs de Jugadores encendido/apagado", "cl_client_17": "<PERSON><PERSON>", "cl_client_18": "alternar modo dios encendido/apagado", "cl_client_19": "ID:", "cl_client_21": "Menú de Jugadores", "cl_client_137": "Info del Jugador", "cl_client_138": "obtener información de jugadores", "cl_client_22": "<PERSON><PERSON><PERSON>", "cl_client_23": "revivir a este jugador", "cl_client_130": "<PERSON>", "cl_client_131": "dar un artículo a un jugador", "cl_client_24": "Inventario del Jugador", "cl_client_25": "abrir el inventario del jugador, presiona [I] cuando esté abierto", "cl_client_26": "Expulsar <PERSON>", "cl_client_27": "expulsar a un jugador del servidor con razón", "cl_client_28": "<PERSON><PERSON>", "cl_client_29": "banear a un jugador del servidor con razón", "cl_client_30": "<PERSON><PERSON>", "cl_client_31": "ir a un jugador", "cl_client_32": "<PERSON><PERSON><PERSON>", "cl_client_33": "traer un jugador hacia ti", "cl_client_34": "<PERSON><PERSON><PERSON>", "cl_client_35": "alternar congelar jugador encendido/apa<PERSON>", "cl_client_36": "<PERSON><PERSON><PERSON>", "cl_client_37": "alternar espectar a otro jugador encendido/apagado", "cl_client_38": "Menú de Opciones del Servidor", "cl_client_39": "Clima del Servidor", "cl_client_40": "ajustar el clima del servidor", "cl_adminmenu": "Opciones de Admin", "cl_adminmenu_a": "alternado > MODO INVISIBLE <", "cl_client_42": "Invisible Encendido", "cl_client_43": "como puedes ver, eres invisible", "cl_client_44": "<PERSON>", "cl_client_45": "como puedes ver, no eres invisible", "cl_client_46": "Modo Dios Encendido", "cl_client_47": "modo dios ahora está encendido", "cl_adminmenu_b": "alternado > MODO DIOS <", "cl_client_48": "<PERSON><PERSON>", "cl_client_49": "modo dios ahora está a<PERSON>gado", "cl_client_50": "Expulsar <PERSON>", "cl_client_51": "Razón", "cl_client_52": "<PERSON><PERSON>", "cl_client_53": "Tipo de Ban", "cl_client_53_a": "Permanente", "cl_client_53_b": "Temporal", "cl_client_54": "Duración del Ban", "cl_client_55": "1 Hora", "cl_client_56": "6 Horas", "cl_client_57": "12 Horas", "cl_client_58": "1 Día", "cl_client_59": "3 Días", "cl_client_60": "1 Semana", "cl_client_61": "1 Mes", "cl_client_62": "3 Meses", "cl_client_63": "6 Meses", "cl_client_64": "1 Año", "cl_client_65": "Permanente", "cl_client_66": "<PERSON><PERSON><PERSON>", "cl_client_67": " ha sido baneado permanentemente", "cl_client_68": " tiene un ban temporal establecido", "cl_client_132": "Dar <PERSON>í<PERSON> a Jugador", "cl_client_133": "Artículo de Inventario", "cl_client_134": "Cantidad", "cl_client_139": "Info del Jugador Admin", "cl_client_140": "Nombre", "cl_client_141": "Trabajo", "cl_client_142": "<PERSON><PERSON>ajo", "cl_client_143": "Efectivo", "cl_client_144": "<PERSON><PERSON>", "cl_client_153": "Banco", "cl_client_148": "ValBanco", "cl_client_149": "RhoBanco", "cl_client_150": "BlkBanco", "cl_client_151": "ArmBanco", "cl_anim_test": "Probador de Animación", "cl_anim_dictionary": "animDictionary", "cl_anim_name": "nombreAnimación", "cl_anim_flag": "bandera", "cl_anim_length": "longitud: en milisegundos", "cl_coords_menu": "<PERSON><PERSON><PERSON>", "cl_coords_70": "Copiar Vector 2", "cl_coords_71": "copiar coordenadas vector2", "cl_coords_72": "Copiar Vector 3", "cl_coords_73": "copiar coordenadas vector3", "cl_coords_74": "Copiar Vector 4", "cl_coords_75": "copiar coordenadas vector4", "cl_coords_76": "<PERSON><PERSON><PERSON>", "cl_coords_77": "copiar rumbo", "cl_coords_print_list": "Imprimir Lista Encendida", "cl_coords_print_list_a": "Habilitar impresión de coordenadas a una lista", "cl_coords_print_full": "Imprimir Lista Completa", "cl_coords_print_full_a": "Imprimir la lista completa de coordenadas", "cl_coords_78": "Coordenadas Copiadas", "cl_coords_79": "coordenadas vector2 copiadas al portapapeles", "cl_coords_80": "coordenadas vector3 copiadas al portapapeles", "cl_coords_81": "coordenadas vector4 copiadas al portapapeles", "cl_coords_82": "R<PERSON><PERSON>", "cl_coords_83": "rumbo copiado al portapapeles", "cl_coords_printlist": "Imprimir <PERSON>", "cl_coords_printlist_a": "Impresión de lista habilitada. Las coordenadas se añadirán a la lista.", "cl_coords_printlist_b": "Lista copiada al portapapeles", "cl_coords_printlist_c": "La lista está vacía", "cl_dev_menu": "Menú de Desarrollador", "cl_dev_menu_a": "<PERSON><PERSON><PERSON><PERSON>", "cl_dev_menu_b": "aparecer un caballo de admin", "cl_dev_menu_c": "Menú de Copiar Coordenadas", "cl_dev_menu_d": "copiar coordenadas al portapapeles", "cl_dev_menu_e": "Probador de Animación", "cl_dev_menu_f": "probar animaciones", "cl_dev_menu_g": "Hash de Entidad", "cl_dev_menu_h": "obtener hash de entidad", "cl_dev_menu_i": "Alternar IDs de Puerta encendido/apagado", "cl_dev_menu_j": "usado para obtener IDs de puerta", "cl_dev_menu_l": "Generador de Entidades", "cl_dev_menu_m": "usado para generar NPC/animales", "cl_dev_126": "<PERSON><PERSON>", "cl_dev_127": "Obtener Hash de Entidad", "cl_dev_128": "nombre de la entidad", "cl_dev_129": "ejemplo: PROVISION_ALLIGATOR_SKIN", "cl_dev_copy": "Hash de Entidad Copiado", "cl_dev_copy_a": "hash de entidad de", "cl_dev_copy_b": "ha sido copiado al portapapeles", "cl_dev_spawnped": "Generar NPC/Animal", "cl_dev_spawnped1": "nombre del NPC", "cl_dev_spawnped2": "ejemplo: mp_a_c_wolf_01", "cl_dev_spawnped3": "atuendo", "cl_dev_spawnped4": "número de atuendo para NPC/animal", "cl_dev_spawnped5": "distancia", "cl_dev_spawnped6": "distancia de generación desde ti", "cl_dev_spawnped7": "congelar", "cl_dev_spawnped8": "congelar NPC/animal al generar", "cl_dev_spawnped9": "Verdadero", "cl_dev_spawnped10": "<PERSON><PERSON><PERSON>", "cl_dev_spawnped11": "generar muerto", "cl_dev_spawnped12": "generar NPC/animal muerto", "cl_door_on": "IDs de Puerta Encendidos", "cl_door_off": "IDs de Puerta Apagados", "cl_door_id": "ID de Puerta", "cl_door_o": "<PERSON><PERSON><PERSON><PERSON>", "cl_finan_menu": "Finanzas del Jugador", "cl_finan_19": "ID:", "cl_finan_90": "Menú de Opciones Financieras", "cl_finan_122": "Banco: $", "cl_finan_122_a": "Banco Val: $", "cl_finan_122_b": "Banco Rho: $", "cl_finan_122_c": "Banco Blk: $", "cl_finan_122_d": "Banco Arm: $", "cl_finan_123": "Efectivo: $", "cl_finan_123_a": "Dinero su<PERSON>: $", "cl_finan_119": "saldo bancario actual del jugador", "cl_finan_120": "saldo en efectivo actual del jugador", "cl_finan_121": "saldo de dinero sucio actual del jugador", "cl_finan_91": "<PERSON>", "cl_finan_92": "dar dinero al jugador", "cl_finan_93": "<PERSON><PERSON><PERSON>", "cl_finan_94": "quitar dinero al jugador", "cl_finan_95": "Tipo", "cl_finan_96": "elige el tipo para dar al jugador", "cl_finan_2": "Banco E<PERSON>ándar", "cl_finan_2a": "Banco Val", "cl_finan_2b": "Banco Rho", "cl_finan_2c": "Banco Blk", "cl_finan_2d": "Banco Arm", "cl_finan_3": "Efectivo", "cl_finan_3a": "<PERSON><PERSON>", "cl_finan_97": "Cantidad", "cl_finan_98": "¿<PERSON><PERSON><PERSON>to quieres dar?", "cl_finan_99": "elige el tipo para quitar al jugador", "cl_finan_115": "¿<PERSON><PERSON><PERSON>to quieres quitar?", "cl_troll_19": "ID", "cl_troll_84": "<PERSON><PERSON><PERSON>", "cl_troll_85": "Menú de Opciones de Trolleo", "cl_troll_86": "Ataque Sal<PERSON>", "cl_troll_87": "trollear jugador activando un ataque salvaje", "cl_troll_128": "<PERSON>nder Fuego al Jugador", "cl_troll_129": "prender fuego a un jugador", "sv_a": "<PERSON><PERSON><PERSON>", "sv_b": "%s fue baneado por %s por %s", "sv_c": "el sistema te baneó por uso inapropiado", "sv_105": "Fuiste baneado permanentemente por el servidor por: Explotación", "sv_100": "abrir el menú de administrador (Solo Admin)", "sv_d": "Uso no autorizado del Menú de Admin", "sv_e": "con ID de ciudadano de", "sv_f": "intentó usar el menú de admin", "sv_101": "No Permitido", "sv_102": "¡no tienes permitido hacer eso!", "sv_g": "Uso No Autorizado", "sv_h": "con ID de ciudadano de", "sv_i": "baneado por usar resurrección de admin", "sv_j": "baneado por usar abrir inventario", "sv_kicked": "Jugador Expulsado", "sv_kicked_a": "%s fue expulsado por %s por %s", "sv_kicked_b": "baneado por usar expulsión de jugador", "sv_ban": "ANUNCIO", "sv_ban_a": "<PERSON><PERSON><PERSON>", "sv_ban_b": "ha sido baneado", "sv_ban_c": "baneado por usar ir al jugador", "sv_ban_d": "baneado por usar traer jugador", "sv_ban_e": "baneado por usar congelar jugador", "sv_ban_f": "baneado por usar espectar jugador", "sv_ban_g": "baneado por usar ataque salvaje", "sv_ban_h": "baneado por usar prender fuego", "sv_ban_i": "baneado por usar dar objeto", "sv_ban_j": "baneado por usar obtener información del jugador", "sv_103": "Has sido expulsado del servidor", "sv_104": "🔸 Consulta nuestro Discord para más información: ", "sv_106": "Has sido ban<PERSON>o:", "sv_107": "Tu baneo es permanente.", "sv_108": "🔸 Consulta nuestro Discord para más información: ", "sv_109": "El baneo expira: ", "sv_110": "🔸 Consulta nuestro Discord para más información: ", "sv_111": "<PERSON><PERSON><PERSON>", "sv_112": "has congelado al jugador ", "sv_113": "<PERSON><PERSON><PERSON>", "sv_114": "has descongelado al jugador ", "sv_135": "<PERSON><PERSON><PERSON><PERSON>", "sv_136": "objeto enviado con éxito", "sv_finan_116": "Error", "sv_finan_117": "el jugador no tiene suficiente ", "sv_finan_118": " para quitar!"}