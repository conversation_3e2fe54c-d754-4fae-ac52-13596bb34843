[{"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-medic/@ox_lib/init.lua", "mt": 0, "s": 18446744073709551615, "i": "AAAAAAAAAAAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-medic/client/client.lua", "mt": 1738703178, "s": 24875, "i": "/hAAAAAAAwAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-medic/client/job.lua", "mt": 1738703178, "s": 7392, "i": "/xAAAAAAAwAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-medic/config.lua", "mt": 1738703178, "s": 1851, "i": "+xAAAAAAAwAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-medic/fxmanifest.lua", "mt": 1738703178, "s": 528, "i": "/BAAAAAAAwAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-medic/locales/el.json", "mt": 1738703178, "s": 3221, "i": "AREAAAAAAwAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-medic/locales/en.json", "mt": 1738703178, "s": 2067, "i": "AhEAAAAAAwAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-medic/locales/es.json", "mt": 1738703178, "s": 2274, "i": "AxEAAAAAAwAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-medic/locales/fr.json", "mt": 1738703178, "s": 2470, "i": "BBEAAAAAAwAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-medic/locales/it.json", "mt": 1738703178, "s": 2260, "i": "BREAAAAAAwAAAAAAAAAAAA=="}, {"n": "B:/txData/RexshackRedMBuild_9B54B7.base/resources//[framework]/rsg-medic/locales/ro.json", "mt": 1738703178, "s": 2143, "i": "BhEAAAAAAwAAAAAAAAAAAA=="}]