
================================================================
======== Log Rotated - 2/6/2025, 11:40:25                       
================================================================

================================================================
======== FXServer Starting - 2/6/2025, 11:40:36                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 88 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[38;5;66m[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[38;5;83m[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] [0mCreating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[38;5;161m[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[38;5;83m[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[38;5;83m[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[     script:rsg-core] Error parsing script @rsg-core/shared/items.lua in resource rsg-core: @rsg-core/shared/items.lua:6: '}' expected near 'description'
[    c-scripting-core] Failed to load script shared/items.lua.
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[38;5;83m[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for bln_belt_attachments
[           resources] Started resource bln_belt_attachments
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE: [97m [connectqueue] Disabling hardcap 
[38;5;83m[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[38;5;83m[           resources] Started resource menu_base
[38;5;161m[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] [0mStarted resource rsg-adminmenu
[    c-scripting-core] [0mCreating script environments for rsg-aimedic
[           resources] Started resource rsg-aimedic
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] [0mCreating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-menu
[           resources] Started resource rsg-menu
[    c-scripting-core] Creating script environments for rsg-butcher
[           resources] Started resource rsg-butcher
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[38;5;83m[           resources] Started resource rsg-doorlock
[38;5;161m[    c-scripting-core] Creating script environments for rsg-essentials
[38;5;83m[           resources] Started resource rsg-essentials
[    c-scripting-core] [0mCreating script environments for rsg-fishing
[38;5;83m[           resources] Started resource rsg-fishing
[38;5;161m[    c-scripting-core] Creating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] Creating script environments for rsg-haydelivery
[           resources] Started resource rsg-haydelivery
[    c-scripting-core] Creating script environments for rsg-headbag
[           resources] Started resource rsg-headbag
[    c-scripting-core] Creating script environments for rsg-horses
[38;5;83m[           resources] Started resource rsg-horses
[38;5;161m[    c-scripting-core] Creating script environments for rsg-telegram
[38;5;83m[           resources] Started resource rsg-telegram
[38;5;161m[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] [0mCreating script environments for rsg-interiors
[           resources] Started resource rsg-interiors
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] [0mCreating script environments for rsg-items
[           resources] Started resource rsg-items
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-lumberjack-main
[           resources] Started resource rsg-lumberjack-main
[    c-scripting-core] Creating script environments for rsg-medic
[38;5;83m[           resources] Started resource rsg-medic
[    c-scripting-core] [0mCreating script environments for rsg-mining
[           resources] Started resource rsg-mining
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-notify
[           resources] Started resource rsg-notify
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[38;5;161m[    c-scripting-core] [0mCreating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[38;5;83m[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[    script:rsg-shops] Error parsing script @rsg-shops/config.lua in resource rsg-shops: @rsg-shops/config.lua:57: '}' expected near '='
[    c-scripting-core] Failed to load script config.lua.
[           resources] Started resource rsg-shops
[    c-scripting-core] [0mCreating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.2
[script:rsg-appearanc] [rsg-appearance] You are currently running an outdated version, please update to version 2.4.2
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[     script:rsg-menu] [rsg-menu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] You are currently running an outdated version, please update to version 2.0.0
[script:rsg-interiors] [rsg-interiors] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.5
[    script:rsg-medic] [rsg-medic] You are currently running an outdated version, please update to version 2.0.5
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Currently unable to run a version check.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[ citizen-server-impl] [0m        fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[script:bln_belt_atta] [bln_belt_attachments] ✓ Up to date (v1.2.1)
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [97m[txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/6/2025, 11:45:06                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 88 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[38;5;161m[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[38;5;161m[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[     script:rsg-core] [0mError parsing script @rsg-core/shared/items.lua in resource rsg-core: @rsg-core/shared/items.lua:6: '}' expected near 'description'
[    c-scripting-core] Failed to load script shared/items.lua.
[           resources] Started resource rsg-core
[38;5;161m[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] [0mStarted resource ox_target
[    c-scripting-core] [0mCreating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for bln_belt_attachments
[           resources] Started resource bln_belt_attachments
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] [0mStarted resource progressbar
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-aimedic
[           resources] Started resource rsg-aimedic
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[38;5;161m[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] [0mCreating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] [0mCreating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-menu
[           resources] Started resource rsg-menu
[    c-scripting-core] [0mCreating script environments for rsg-butcher
[           resources] Started resource rsg-butcher
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] [0mStarted resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] [0mCreating script environments for rsg-haydelivery
[           resources] Started resource rsg-haydelivery
[    c-scripting-core] Creating script environments for rsg-headbag
[           resources] Started resource rsg-headbag
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] [0mStarted resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-interiors
[           resources] Started resource rsg-interiors
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-items
[           resources] Started resource rsg-items
[    c-scripting-core] [0mCreating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-lumberjack-main
[           resources] Started resource rsg-lumberjack-main
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] [0mStarted resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-mining
[           resources] Started resource rsg-mining
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-notify
[           resources] Started resource rsg-notify
[    c-scripting-core] [0mCreating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.2
[script:rsg-appearanc] [rsg-appearance] You are currently running an outdated version, please update to version 2.4.2
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[     script:rsg-menu] [rsg-menu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] You are currently running an outdated version, please update to version 2.0.0
[script:rsg-interiors] [rsg-interiors] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.5
[    script:rsg-medic] [rsg-medic] You are currently running an outdated version, please update to version 2.0.5
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[38;5;229m[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-radialmen] [rsg-radialmenu] Currently unable to run a version check.
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[script:bln_belt_atta] [0m[bln_belt_attachments] ✓ Up to date (v1.2.1)
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/6/2025, 11:51:49                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 88 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[38;5;161m[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] [0mStarted resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[     script:rsg-core] [97mError parsing script @rsg-core/shared/items.lua in resource rsg-core: @rsg-core/shared/items.lua:6: '}' expected near 'description'
[    c-scripting-core] Failed to load script shared/items.lua.
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] [97mStarted resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] [0mStarted resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] [97mStarted resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for bln_belt_attachments
[           resources] Started resource bln_belt_attachments
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[38;5;161m[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-aimedic
[           resources] Started resource rsg-aimedic
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] [0mStarted resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[38;5;161m[    c-scripting-core] Creating script environments for rsg-menu
[           resources] Started resource rsg-menu
[38;5;161m[    c-scripting-core] Creating script environments for rsg-butcher
[           resources] Started resource rsg-butcher
[    c-scripting-core] [0mCreating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] [0mStarted resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] [0mCreating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] [0mCreating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] [0mCreating script environments for rsg-haydelivery
[           resources] Started resource rsg-haydelivery
[    c-scripting-core] Creating script environments for rsg-headbag
[           resources] Started resource rsg-headbag
[    c-scripting-core] [0mCreating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] [0mCreating script environments for rsg-telegram
[           resources] [0mStarted resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] [0mStarted resource rsg-hud
[    c-scripting-core] [0mCreating script environments for rsg-interiors
[           resources] Started resource rsg-interiors
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-items
[           resources] Started resource rsg-items
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-lumberjack-main
[           resources] Started resource rsg-lumberjack-main
[    c-scripting-core] [0mCreating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-mining
[           resources] Started resource rsg-mining
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-notify
[           resources] Started resource rsg-notify
[38;5;161m[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] [0mCreating script environments for rsg-radialmenu
[           resources] [0mStarted resource rsg-radialmenu
[    c-scripting-core] [0mCreating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.2
[script:rsg-appearanc] [rsg-appearance] You are currently running an outdated version, please update to version 2.4.2
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.2
[script:rsg-appearanc] [rsg-appearance] You are currently running an outdated version, please update to version 2.4.2
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[     script:rsg-menu] [rsg-menu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[38;5;130m[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[      script:rsg-hud] [rsg-hud] You are currently running an outdated version, please update to version 2.0.0
[script:rsg-interiors] [36m[rsg-interiors] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.5
[    script:rsg-medic] [rsg-medic] You are currently running an outdated version, please update to version 2.0.5
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.5
[    script:rsg-medic] [rsg-medic] You are currently running an outdated version, please update to version 2.0.5
[script:rsg-playerinf] [36m[rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Currently unable to run a version check.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[script:bln_belt_atta] [bln_belt_attachments] ✓ Up to date (v1.2.1)
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/6/2025, 11:53:19                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 88 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] [0mStarted resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[     script:rsg-core] Error parsing script @rsg-core/shared/items.lua in resource rsg-core: @rsg-core/shared/items.lua:6: '}' expected near 'description'
[    c-scripting-core] Failed to load script shared/items.lua.
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] [97mCreating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] [0mCreating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for bln_belt_attachments
[           resources] Started resource bln_belt_attachments
[    c-scripting-core] [0mCreating script environments for connectqueue
[ script:connectqueue] QUEUE: [91m [connectqueue] Disabling hardcap 
[           resources] [0mStarted resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] [0mCreating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] [0mCreating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-aimedic
[           resources] [0mStarted resource rsg-aimedic
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] [0mCreating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] [0mCreating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] [0mStarted resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-menu
[           resources] [0mStarted resource rsg-menu
[    c-scripting-core] Creating script environments for rsg-butcher
[           resources] Started resource rsg-butcher
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[38;5;161m[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[38;5;161m[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] [0mCreating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] Creating script environments for rsg-haydelivery
[           resources] [0mStarted resource rsg-haydelivery
[    c-scripting-core] Creating script environments for rsg-headbag
[           resources] Started resource rsg-headbag
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] [0mCreating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-interiors
[           resources] Started resource rsg-interiors
[    c-scripting-core] [0mCreating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-items
[           resources] Started resource rsg-items
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-lumberjack-main
[           resources] Started resource rsg-lumberjack-main
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-mining
[           resources] Started resource rsg-mining
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-notify
[           resources] Started resource rsg-notify
[    c-scripting-core] [0mCreating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] [0mCreating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.2
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.2
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[     script:rsg-menu] [rsg-menu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] You are currently running an outdated version, please update to version 2.0.0
[script:rsg-interiors] [rsg-interiors] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.5
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.5
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0[0m
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-radialmen] [0m[rsg-radialmenu] Currently unable to run a version check.
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] [91mcc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[script:bln_belt_atta] [bln_belt_attachments] ✓ Up to date (v1.2.1)
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/6/2025, 12:04:38                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 88 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[38;5;66m[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] [97mThe file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[38;5;83m[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] [97mCreating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[38;5;83m[           resources] Started resource rsg-menubase
[    c-scripting-core] [0mCreating script environments for ox_target
[           resources] [97mStarted resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for bln_belt_attachments
[           resources] Started resource bln_belt_attachments
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] [0mQUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] [0mCreating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] [0mStarted resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-aimedic
[           resources] Started resource rsg-aimedic
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] [0mStarted resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[38;5;161m[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] [0mCreating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-menu
[           resources] Started resource rsg-menu
[    c-scripting-core] [0mCreating script environments for rsg-butcher
[           resources] Started resource rsg-butcher
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] [0mStarted resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[38;5;83m[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] [0mCreating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] [0mCreating script environments for rsg-haydelivery
[           resources] Started resource rsg-haydelivery
[    c-scripting-core] Creating script environments for rsg-headbag
[           resources] Started resource rsg-headbag
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-interiors
[           resources] Started resource rsg-interiors
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-items
[38;5;83m[           resources] Started resource rsg-items
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-lumberjack-main
[           resources] Started resource rsg-lumberjack-main
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-mining
[           resources] Started resource rsg-mining
[    c-scripting-core] [0mCreating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] [0mCreating script environments for rsg-notify
[           resources] Started resource rsg-notify
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] [0mStarted resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.2
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.2
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[     script:rsg-menu] [rsg-menu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[      script:rsg-hud] [rsg-hud] You are currently running an outdated version, please update to version 2.0.0
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[script:rsg-interiors] [rsg-interiors] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.5
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.5
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Currently unable to run a version check.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[script:bln_belt_atta] [bln_belt_attachments] ✓ Up to date (v1.2.1)
[ citizen-server-impl] [97m        fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/

================================================================
======== FXServer Starting - 2/6/2025, 14:25:42                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 88 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] [0mStarted resource rsg-core
[    c-scripting-core] [97mCreating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] [0mStarted resource rsg-spawn
[    c-scripting-core] [0mCreating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for bln_belt_attachments
[           resources] Started resource bln_belt_attachments
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE: [91m [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] [0mCreating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-aimedic
[           resources] Started resource rsg-aimedic
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[38;5;161m[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-menu
[           resources] Started resource rsg-menu
[    c-scripting-core] [0mCreating script environments for rsg-butcher
[           resources] Started resource rsg-butcher
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] [0mCreating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[38;5;161m[    c-scripting-core] Creating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] [0mCreating script environments for rsg-haydelivery
[           resources] Started resource rsg-haydelivery
[38;5;161m[    c-scripting-core] Creating script environments for rsg-headbag
[           resources] Started resource rsg-headbag
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-interiors
[           resources] Started resource rsg-interiors
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-items
[           resources] Started resource rsg-items
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-lumberjack-main
[           resources] Started resource rsg-lumberjack-main
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-mining
[           resources] Started resource rsg-mining
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-notify
[           resources] Started resource rsg-notify
[    c-scripting-core] [0mCreating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] [0mCreating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.2
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.2
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[     script:rsg-menu] [rsg-menu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] You are currently running an outdated version, please update to version 2.0.0
[script:rsg-interiors] [rsg-interiors] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.5
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.5
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Currently unable to run a version check.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[ citizen-server-impl] server thread hitch warning: timer interval of 283 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[script:bln_belt_atta] [bln_belt_attachments] ✓ Up to date (v1.2.1)
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] is loading into the server
[ citizen-server-impl] sync thread hitch warning: timer interval of 159 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 210 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 616 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 238 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 114 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-multicharacter took 679.6260ms to execute a query!
[      script:oxmysql] SELECT * FROM players WHERE license = ? ["license:10de59f3e77fd041e517a172faf4b229c64bea90"]
[ citizen-server-impl] server thread hitch warning: timer interval of 764 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 475 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 181 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 166 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 303 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 210 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[script:rsg-multichar] [rsg-core] Babygang (Citizen ID: ZDL62613) has succesfully loaded!
[ citizen-server-impl] server thread hitch warning: timer interval of 496 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-core took 325.2229ms to execute a query!
[      script:oxmysql] INSERT INTO players (citizenid, cid, license, name, money, charinfo, job, gang, position, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE cid = ?, name = ?, money = ?, charinfo = ?, job = ?, gang = ?, position = ?, metadata = ? ["ZDL62613",1,"license:10de59f3e77fd041e517a172faf4b229c64bea90","Babygang","{\"bloodmoney\":0,\"armbank\":0,\"cash\":97249.4975,\"valbank\":0,\"rhobank\":0,\"blkbank\":0,\"bank\":6}","{\"birthdate\":\"1870-01-01\",\"gender\":0,\"account\":\"US04RSGCore6183584342\",\"firstname\":\"SLENTENG\",\"nationality\":\"INDONESIA\",\"lastname\":\"SALOMO\",\"cid\":\"1\"}","{\"label\":\"Strawberry Law Enforcement\",\"payment\":25,\"isboss\":false,\"onduty\":false,\"name\":\"strlaw\",\"type\":\"leo\",\"grade\":{\"name\":\"Deputy\",\"payment\":25,\"level\":1,\"isboss\":false}}","{\"label\":\"No Gang\",\"name\":\"none\",\"grade\":{\"name\":\"Unaffiliated\",\"level\":0,\"isboss\":false},\"isboss\":false}","{\"x\":-562.************,\"y\":-3776.**********,\"z\":238.*************}","{\"criminalrecord\":{\"hasRecord\":false},\"hunger\":100,\"health\":600,\"bloodtype\":\"AB-\",\"stress\":0,\"cleanliness\":100.0,\"status\":[],\"isdead\":false,\"fingerprint\":\"vz977H61SAo4118\",\"rep\":[],\"armor\":0,\"jailitems\":[],\"thirst\":100,\"injail\":0,\"walletid\":\"RSG-********\",\"ishandcuffed\":false,\"callsign\":\"NO CALLSIGN\"}",1,"Babygang","{\"bloodmoney\":0,\"armbank\":0,\"cash\":97249.4975,\"valbank\":0,\"rhobank\":0,\"blkbank\":0,\"bank\":6}","{\"birthdate\":\"1870-01-01\",\"gender\":0,\"account\":\"US04RSGCore6183584342\",\"firstname\":\"SLENTENG\",\"nationality\":\"INDONESIA\",\"lastname\":\"SALOMO\",\"cid\":\"1\"}","{\"label\":\"Strawberry Law Enforcement\",\"payment\":25,\"isboss\":false,\"onduty\":false,\"name\":\"strlaw\",\"type\":\"leo\",\"grade\":{\"name\":\"Deputy\",\"payment\":25,\"level\":1,\"isboss\":false}}","{\"label\":\"No Gang\",\"name\":\"none\",\"grade\":{\"name\":\"Unaffiliated\",\"level\":0,\"isboss\":false},\"isboss\":false}","{\"x\":-562.************,\"y\":-3776.**********,\"z\":238.*************}","{\"criminalrecord\":{\"hasRecord\":false},\"hunger\":100,\"health\":600,\"bloodtype\":\"AB-\",\"stress\":0,\"cleanliness\":100.0,\"status\":[],\"isdead\":false,\"fingerprint\":\"vz977H61SAo4118\",\"rep\":[],\"armor\":0,\"jailitems\":[],\"thirst\":100,\"injail\":0,\"walletid\":\"RSG-********\",\"ishandcuffed\":false,\"callsign\":\"NO CALLSIGN\"}"]
[      script:oxmysql] [0m[11.6.2-MariaDB] rsg-inventory took 385.3309ms to execute a query!
[      script:oxmysql] UPDATE players SET inventory = ? WHERE citizenid = ? ["[{\"slot\":1,\"type\":\"item\",\"amount\":50,\"info\":[],\"name\":\"bread\"},{\"slot\":2,\"type\":\"item\",\"amount\":49,\"info\":[],\"name\":\"water\"},{\"slot\":3,\"type\":\"item\",\"amount\":50,\"info\":[],\"name\":\"horse_brush\"},{\"slot\":4,\"type\":\"item\",\"amount\":50,\"info\":[],\"name\":\"horse_lantern\"},{\"slot\":5,\"type\":\"weapon\",\"amount\":1,\"info\":{\"serie\":\"36fng1DR146FhUu\",\"quality\":99.0},\"name\":\"weapon_repeater_winchester\"},{\"slot\":6,\"type\":\"item\",\"amount\":44,\"info\":[],\"name\":\"ammo_box_revolver\"},{\"slot\":7,\"type\":\"weapon\",\"amount\":1,\"info\":{\"serie\":\"46ybg5fE673OVJh\",\"quality\":96.2},\"name\":\"weapon_repeater_evans\"},{\"slot\":8,\"type\":\"item\",\"amount\":36,\"info\":[],\"name\":\"ammo_box_repeater\"},{\"slot\":10,\"type\":\"weapon\",\"amount\":1,\"info\":{\"serie\":\"75pTe5oA775gcvp\",\"quality\":93.0},\"name\":\"weapon_revolver_cattleman\"}]","ZDL62613"]
[ citizen-server-impl] server thread hitch warning: timer interval of 239 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[script:rsg-inventory] SCRIPT ERROR: @rsg-inventory/server/main.lua:1233: attempt to compare number with nil
[ citizen-server-impl] sync thread hitch warning: timer interval of 114 milliseconds
[script:rsg-inventory] SCRIPT ERROR: @rsg-inventory/server/main.lua:1260: attempt to compare number with nil
[ citizen-server-impl] sync thread hitch warning: timer interval of 145 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 1881 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 211 milliseconds
[    script:rsg-shops] SCRIPT ERROR: @rsg-shops/server/server.lua:14: No such export CreateShop in resource rsg-inventory
[    script:rsg-shops] SCRIPT ERROR: @rsg-shops/server/server.lua:14: No such export CreateShop in resource rsg-inventory
[   script:rsg-lawman] SCRIPT ERROR: @rsg-lawman/server/server.lua:252: No such export OpenInventory in resource rsg-inventory
[    script:rsg-shops] SCRIPT ERROR: @rsg-shops/server/server.lua:14: No such export CreateShop in resource rsg-inventory
[ citizen-server-impl] server thread hitch warning: timer interval of 157 milliseconds
[    script:rsg-shops] SCRIPT ERROR: @rsg-shops/server/server.lua:14: No such export CreateShop in resource rsg-inventory
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 156 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!

================================================================
======== FXServer Starting - 2/6/2025, 14:48:04                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] [32mScanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 88 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] [0mCreating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] [97mArgument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] [97mStarted resource rsg-core
[    c-scripting-core] [97mCreating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] [0mCreating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for bln_belt_attachments
[           resources] Started resource bln_belt_attachments
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE: [91m [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[38;5;161m[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] [0mCreating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] [0mCreating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[38;5;161m[    c-scripting-core] Creating script environments for rsg-aimedic
[           resources] Started resource rsg-aimedic
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[38;5;161m[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-menu
[           resources] Started resource rsg-menu
[    c-scripting-core] Creating script environments for rsg-butcher
[           resources] Started resource rsg-butcher
[    c-scripting-core] [0mCreating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] Creating script environments for rsg-haydelivery
[           resources] Started resource rsg-haydelivery
[    c-scripting-core] Creating script environments for rsg-headbag
[           resources] Started resource rsg-headbag
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] [0mStarted resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-interiors
[           resources] Started resource rsg-interiors
[    c-scripting-core] [0mCreating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-items
[           resources] Started resource rsg-items
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-lumberjack-main
[           resources] [0mStarted resource rsg-lumberjack-main
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] [0mCreating script environments for rsg-mining
[           resources] Started resource rsg-mining
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-notify
[           resources] Started resource rsg-notify
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[38;5;161m[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[38;5;68m[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.2
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.2
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[     script:rsg-menu] [rsg-menu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] You are currently running an outdated version, please update to version 2.0.0
[script:rsg-interiors] [rsg-interiors] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.2.0
[script:rsg-inventory] [rsg-inventory] You are currently running an outdated version, please update to version 2.2.0
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.5
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.5
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Currently unable to run a version check.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[script:rsg-inventory] 2 inventories successfully loaded
[    script:redm-ipls] [36m[redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[script:bln_belt_atta] [bln_belt_attachments] ✓ Up to date (v1.2.1)
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/6/2025, 14:53:11                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 88 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] [0mCreating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] [97mStarted resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] [0mStarted resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] [0mCreating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for bln_belt_attachments
[           resources] Started resource bln_belt_attachments
[    c-scripting-core] [97mCreating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[38;5;83m[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-aimedic
[           resources] Started resource rsg-aimedic
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-menu
[           resources] Started resource rsg-menu
[    c-scripting-core] Creating script environments for rsg-butcher
[           resources] [0mStarted resource rsg-butcher
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] [0mCreating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-fishprep
[           resources] Started resource rsg-fishprep
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] [0mCreating script environments for rsg-haydelivery
[           resources] Started resource rsg-haydelivery
[    c-scripting-core] [0mCreating script environments for rsg-headbag
[           resources] Started resource rsg-headbag
[    c-scripting-core] [0mCreating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[38;5;161m[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-interiors
[           resources] Started resource rsg-interiors
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-items
[           resources] Started resource rsg-items
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-lumberjack-main
[           resources] Started resource rsg-lumberjack-main
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] [0mCreating script environments for rsg-mining
[           resources] Started resource rsg-mining
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-notify
[           resources] Started resource rsg-notify
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] [0mCreating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.2
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.2
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[     script:rsg-menu] [rsg-menu] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] You are currently running an outdated version, please update to version 2.0.0
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-interiors] [rsg-interiors] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.2.0
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.2.0
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 2 inventories successfully loaded
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.5
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.5
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[script:rsg-radialmen] [36m[rsg-radialmenu] Currently unable to run a version check.
[script:bln_belt_atta] [93m[bln_belt_attachments] ✓ Up to date (v1.2.1)
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[ citizen-server-impl] server thread hitch warning: timer interval of 178 milliseconds
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] is loading into the server
[ citizen-server-impl] network thread hitch warning: timer interval of 520 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 508 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 595 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 597 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 253 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 160 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 178 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 471 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 213 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-multicharacter took 368.2275ms to execute a query!
[      script:oxmysql] SELECT * FROM players WHERE license = ? ["license:10de59f3e77fd041e517a172faf4b229c64bea90"]
[ citizen-server-impl] server thread hitch warning: timer interval of 355 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 183 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 897 milliseconds
[     script:rsg-core] [rsg-core:LOG][0m Babygang PLAYER SAVED!
[script:rsg-multichar] [rsg-core] Babygang (Citizen ID: ZDL62613) has succesfully loaded!
[ citizen-server-impl] server thread hitch warning: timer interval of 174 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] sync thread hitch warning: timer interval of 102 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 120 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 245 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 474 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 385 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 288 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 153 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 463 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 152 milliseconds
[     script:rsg-core] [rsg-core:LOG][0m Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[    script:rsg-shops] SCRIPT ERROR: @rsg-shops/server/server.lua:17: attempt to get length of a nil value (local 'itemTable')
[ citizen-server-impl] server thread hitch warning: timer interval of 211 milliseconds
[38;5;109m[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 2346 milliseconds
