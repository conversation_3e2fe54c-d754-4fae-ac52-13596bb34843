local RSGCore = exports['rsg-core']:GetCoreObject()

local Active = false
local medicHorse = nil
local medicPed = nil
local lastCallTime = {}
local CooldownActive = false
local lastMedicPos = vector3(0,0,0)

local function IsPlayerMedic()
    local Player = RSGCore.Functions.GetPlayerData()
    return Player.job.name == "medic"
end


local function CleanupEntities()
    if DoesEntityExist(medicHorse) then
        DeletePed(medicHorse)
    end
    if DoesEntityExist(medicPed) then
        DeletePed(medicPed)
    end
end

local function ResetScriptState()
    Active = false
    medicHorse = nil
    medicPed = nil
    CooldownActive = false
end

local function Notify(msg, type, duration)
    RSGCore.Functions.Notify(msg, type, duration)
end

local function FindSafeRoadSpawnPoint(playerCoords, radius)
    local attempt = 0
    local maxAttempts = 100  

    while attempt < maxAttempts do
        local angle = math.random() * 2 * math.pi
        local checkPoint = vector3(
            playerCoords.x + radius * math.cos(angle),
            playerCoords.y + radius * math.sin(angle),
            playerCoords.z
        )

        local retval, outPosition = GetClosestVehicleNode(checkPoint.x, checkPoint.y, checkPoint.z, 1, 3.0, 0)

        if retval then
            local roadPosition = vector3(outPosition.x, outPosition.y, outPosition.z)
            if not IsPositionOccupied(roadPosition.x, roadPosition.y, roadPosition.z, 10.0, false, true, true, false, false, 0, false) then
                local _, groundZ = GetGroundZFor_3dCoord(roadPosition.x, roadPosition.y, roadPosition.z + 10.0, 0)
                roadPosition = vector3(roadPosition.x, roadPosition.y, groundZ)
                return roadPosition
            end
        end

        attempt = attempt + 1
        radius = radius + 5.0  
    end

    return nil  
end

-- Main Command
RegisterCommand("help", function(source, args, raw)
    local playerPed = PlayerPedId()
    local _source = source
    local callCooldown = 1 * 60 * 1000 -- 1 minutes in milliseconds
    local currentTime = GetGameTimer()

    if IsEntityDead(playerPed) then
        if IsPlayerMedic() then
            Notify("As a medic, you can't call for medical assistance. Use your skills!", 'error', 3000)
        elseif lastCallTime[_source] and currentTime - lastCallTime[_source] < callCooldown then
            local remainingTime = callCooldown - (currentTime - lastCallTime[_source])
            local remainingMinutes = math.floor(remainingTime / (60 * 1000))
            Notify("Please wait " .. remainingMinutes .. " minutes before calling for help again.", 'error', 4000)
        else
            
            RSGCore.Functions.TriggerCallback('rsg-medic:server:anyMedicsOnline', function(medicsOnline)
                if medicsOnline then
                    Notify("There are medics on duty. Please wait for their assistance", 'info', 5000)
                else
                    Notify("A medic is on the way!", 'success', 3000)
                    TriggerEvent('rsg-medic:client:spawnMedic')
                    lastCallTime[_source] = currentTime
                end
            end)
        end
    else
        Notify("This can only be used when dead!", 'error', 3000)
    end
end)


-- Medic Spawn Event
RegisterNetEvent('rsg-medic:client:spawnMedic')
AddEventHandler('rsg-medic:client:spawnMedic', function()
    CleanupEntities()
    CooldownActive = true

    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local spawnPos = FindSafeRoadSpawnPoint(playerCoords, 25.0)

    if not spawnPos then
        Notify("Unable to find a suitable spawn location for the medic.", 'error', 5000)
        return
    end

    local horseHash = GetHashKey("A_C_Horse_Arabian_White")
    local pedModelHash = GetHashKey("cs_sddoctor_01")

    RequestModel(horseHash)
    RequestModel(pedModelHash)

    while not HasModelLoaded(horseHash) or not HasModelLoaded(pedModelHash) do
        Wait(1)
    end

    medicHorse = CreatePed(horseHash, spawnPos.x, spawnPos.y, spawnPos.z, 0.0, true, false)
    Citizen.InvokeNative(0x283978A15512B2FE, medicHorse, true)

    
    Citizen.InvokeNative(0xD3A7B003ED343FD9, medicHorse, 0x20359E53, true, true, true) -- Saddle
    Citizen.InvokeNative(0xD3A7B003ED343FD9, medicHorse, 0x508B80B9, true, true, true) -- Blanket
    Citizen.InvokeNative(0xD3A7B003ED343FD9, medicHorse, 0xF0C30271, true, true, true) -- Bag
    Citizen.InvokeNative(0xD3A7B003ED343FD9, medicHorse, 0x12F0DF9F, true, true, true) -- Bedroll
    Citizen.InvokeNative(0xD3A7B003ED343FD9, medicHorse, 0x67AF7302, true, true, true) -- Stirrups
    Citizen.InvokeNative(0xD3A7B003ED343FD9, medicHorse, 0x635E387C, true, true, true) -- Lantern


    
    Citizen.InvokeNative(0xA95F667A755725DA, medicHorse, 1.9)
    Citizen.InvokeNative(0x4EB122210A90E2D8, medicHorse, 1.9)

    medicPed = CreatePed(pedModelHash, spawnPos.x, spawnPos.y, spawnPos.z, 0.0, true, false)
    Citizen.InvokeNative(0x283978A15512B2FE, medicPed, true)

    Citizen.InvokeNative(0x028F76B6E78246EB, medicPed, medicHorse, -1)
    
    TaskGoToCoordAnyMeans(medicPed, playerCoords.x, playerCoords.y, playerCoords.z, 4.0, 0, 0, 786603, 0xbf800000)

    Active = true
    
end)


Citizen.CreateThread(function()
    while true do
        Citizen.Wait(3000) 
        if Active and DoesEntityExist(medicPed) then
            local playerCoords = GetEntityCoords(PlayerPedId())
            TaskGoToCoordAnyMeans(medicPed, playerCoords.x, playerCoords.y, playerCoords.z, 4.0, 0, 0, 786603, 0xbf800000)
        end
    end
end)


Citizen.CreateThread(function()
    while true do
        Citizen.Wait(10000)  -- Check every 10 seconds
        if Active and DoesEntityExist(medicPed) then
            local currentPos = GetEntityCoords(medicPed)
            if #(currentPos - lastMedicPos) < 0.1 then
                -- Medic hasn't moved, try to unstuck
                local playerCoords = GetEntityCoords(PlayerPedId())
                SetEntityCoordsNoOffset(medicPed, playerCoords.x + 3.0, playerCoords.y + 3.0, playerCoords.z, false, false, false)
                TaskGoToCoordAnyMeans(medicPed, playerCoords.x, playerCoords.y, playerCoords.z, 4.0, 0, 0, 786603, 0xbf800000)
            end
            lastMedicPos = currentPos
        end
    end
end)


Citizen.CreateThread(function()
    while true do
        Citizen.Wait(200)
        if Active then
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            local medicCoords = GetEntityCoords(medicPed)
            local distToMedicPed = #(playerCoords - medicCoords)
            
            if distToMedicPed <= 15.0 and IsPedOnMount(medicPed) then
                
                TaskDismountAnimal(medicPed, 1, 0, 0, 0, 0)
                Citizen.Wait(1000)  
            end
            
            if distToMedicPed <= 3.0 then  
                
                ClearPedTasks(medicPed)
                
                if IsEntityDead(playerPed) then
                    Notify("The medic is treating you...", 'primary', 3000)
                    
                    
                    
                    TaskStartScenarioInPlace(medicPed, GetHashKey("WORLD_HUMAN_CROUCH_INSPECT"), 0, true)
                    Citizen.Wait(6000)  
                    
                    Citizen.Wait(Config.ReviveTime) 
                    
                    TriggerEvent('rsg-medic:client:revive')
                    TriggerServerEvent('hhfw:charge')  
                    Notify("The medic has revived you and returned you to a safe zone!", 'success', 3000)
                    
                    CleanupEntities()
                    ResetScriptState()
                else
                    Notify("The medic has arrived but you don't need treatment.", 'primary', 3000)
                    CleanupEntities()
                    ResetScriptState()
                end
            end
        else
            Citizen.Wait(5000)  
        end
    end
end)


Citizen.CreateThread(function()
    while true do
        Citizen.Wait(60000)  
        if CooldownActive then
            CooldownActive = false
        end
    end
end)

