{"manifest": {"name": "@citizenfx/protobufjs", "version": "6.8.8", "versionScheme": "~", "description": "Protocol Buffers for JavaScript (& TypeScript).", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/dcodeIO/protobuf.js.git"}, "bugs": {"url": "https://github.com/dcodeIO/protobuf.js/issues"}, "homepage": "http://dcode.io/protobuf.js", "keywords": ["protobuf", "protocol-buffers", "serialization", "typescript"], "main": "index.js", "types": "index.d.ts", "bin": {"pbjs": "bin\\pbjs", "pbts": "bin\\pbts"}, "scripts": {"bench": "node bench", "build": "gulp --gulpfile scripts/gulpfile.js", "changelog": "node scripts/changelog -w", "coverage": "istanbul --config=config/istanbul.json cover node_modules/tape/bin/tape tests/*.js tests/node/*.js", "docs": "jsdoc -c config/jsdoc.json -R README.md --verbose --pedantic", "lint": "eslint **/*.js -c config/eslint.json && tslint **/*.d.ts -e **/node_modules/** -t stylish -c config/tslint.json", "pages": "node scripts/pages", "postinstall": "node scripts/postinstall", "prof": "node bench/prof", "test": "tape -r ./lib/tape-adapter tests/*.js tests/node/*.js", "test-types": "tsc tests/comp_typescript.ts --lib es2015 --strictNull<PERSON>hecks --experimentalDecorators --emitDecoratorMetadata && tsc tests/data/test.js.ts --lib es2015 --noEmit --strictNullChecks && tsc tests/data/rpc.ts --lib es2015 --noEmit --strictNullChecks", "types": "node bin/pbts --main --global protobuf --out index.d.ts src/ lib/aspromise/index.js lib/base64/index.js lib/codegen/index.js lib/eventemitter/index.js lib/float/index.js lib/fetch/index.js lib/inquire/index.js lib/path/index.js lib/pool/index.js lib/utf8/index.js && npm run test-types", "make": "npm run test && npm run types && npm run build && npm run lint", "release": "npm run make && npm run changelog"}, "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/long": "^4.0.0", "@types/node": "^10.1.0", "long": "^4.0.0"}, "devDependencies": {"benchmark": "^2.1.4", "browserify": "^16.2.2", "browserify-wrap": "^1.0.2", "bundle-collapser": "^1.3.0", "chalk": "^2.4.1", "escodegen": "^1.9.1", "eslint": "^4.19.1", "espree": "^3.5.4", "estraverse": "^4.2.0", "gh-pages": "^1.1.0", "git-raw-commits": "^1.3.6", "git-semver-tags": "^1.3.6", "glob": "^7.1.2", "google-protobuf": "^3.5.0", "gulp": "^4.0.0", "gulp-header": "^2.0.5", "gulp-if": "^2.0.1", "gulp-sourcemaps": "^2.6.4", "gulp-uglify": "^3.0.0", "istanbul": "^0.4.5", "jaguarjs-jsdoc": "github:dcodeIO/jaguarjs-jsdoc", "jsdoc": "^3.5.5", "minimist": "^1.2.0", "reflect-metadata": "^0.1.12", "semver": "^5.5.0", "tape": "^4.9.0", "tmp": "0.0.33", "tslint": "^5.10.0", "typescript": "^2.8.3", "uglify-js": "^3.3.25", "vinyl-buffer": "^1.0.1", "vinyl-fs": "^3.0.3", "vinyl-source-stream": "^2.0.0"}, "cliDependencies": ["semver", "chalk", "glob", "jsdoc", "minimist", "tmp", "uglify-js", "espree", "escodegen", "estraverse"], "files": ["index.js", "index.d.ts", "light.d.ts", "light.js", "minimal.d.ts", "minimal.js", "package-lock.json", "tsconfig.json", "scripts/postinstall.js", "bin/**", "cli/**", "dist/**", "ext/**", "google/**", "src/**"], "_registry": "npm", "_loc": "B:\\txData\\RexshackRedMBuild_9B54B7.base\\cache\\yarn-cache\\v6\\npm-@citizenfx-protobufjs-6.8.8-d12edcada06182f3785dea1d35eebf0ebe4fd2c3-integrity\\node_modules\\@citizenfx\\protobufjs\\package.json", "readmeFilename": "README.md", "readme": "<h1><p align=\"center\"><img alt=\"protobuf.js\" src=\"https://github.com/dcodeIO/protobuf.js/raw/master/pbjs.png\" width=\"120\" height=\"104\" /></p></h1>\n<p align=\"center\"><a href=\"https://npmjs.org/package/protobufjs\"><img src=\"https://img.shields.io/npm/v/protobufjs.svg\" alt=\"\"></a> <a href=\"https://travis-ci.org/dcodeIO/protobuf.js\"><img src=\"https://travis-ci.org/dcodeIO/protobuf.js.svg?branch=master\" alt=\"\"></a> <a href=\"https://npmjs.org/package/protobufjs\"><img src=\"https://img.shields.io/npm/dm/protobufjs.svg\" alt=\"\"></a> <a href=\"https://www.paypal.com/cgi-bin/webscr?cmd=_donations&business=dcode%40dcode.io&item_name=Open%20Source%20Software%20Donation&item_number=dcodeIO%2Fprotobuf.js\"><img alt=\"donate ❤\" src=\"https://img.shields.io/badge/donate-❤-ff2244.svg\"></a></p>\n\n**Protocol Buffers** are a language-neutral, platform-neutral, extensible way of serializing structured data for use in communications protocols, data storage, and more, originally designed at Google ([see](https://developers.google.com/protocol-buffers/)).\n\n**protobuf.js** is a pure JavaScript implementation with [TypeScript](https://www.typescriptlang.org) support for [node.js](https://nodejs.org) and the browser. It's easy to use, blazingly fast and works out of the box with [.proto](https://developers.google.com/protocol-buffers/docs/proto) files!\n\nContents\n--------\n\n* [Installation](#installation)<br />\n  How to include protobuf.js in your project.\n\n* [Usage](#usage)<br />\n  A brief introduction to using the toolset.\n\n  * [Valid Message](#valid-message)\n  * [Toolset](#toolset)<br />\n\n* [Examples](#examples)<br />\n  A few examples to get you started.\n\n  * [Using .proto files](#using-proto-files)\n  * [Using JSON descriptors](#using-json-descriptors)\n  * [Using reflection only](#using-reflection-only)\n  * [Using custom classes](#using-custom-classes)\n  * [Using services](#using-services)\n  * [Usage with TypeScript](#usage-with-typescript)<br />\n\n* [Command line](#command-line)<br />\n  How to use the command line utility.\n\n  * [pbjs for JavaScript](#pbjs-for-javascript)\n  * [pbts for TypeScript](#pbts-for-typescript)\n  * [Reflection vs. static code](#reflection-vs-static-code)\n  * [Command line API](#command-line-api)<br />\n\n* [Additional documentation](#additional-documentation)<br />\n  A list of available documentation resources.\n\n* [Performance](#performance)<br />\n  A few internals and a benchmark on performance.\n\n* [Compatibility](#compatibility)<br />\n  Notes on compatibility regarding browsers and optional libraries.\n\n* [Building](#building)<br />\n  How to build the library and its components yourself.\n\nInstallation\n---------------\n\n### node.js\n\n```\n$> npm install protobufjs [--save --save-prefix=~]\n```\n\n```js\nvar protobuf = require(\"protobufjs\");\n```\n\n**Note** that this library's versioning scheme is not semver-compatible for historical reasons. For guaranteed backward compatibility, always depend on `~6.A.B` instead of `^6.A.B` (hence the `--save-prefix` above).\n\n### Browsers\n\nDevelopment:\n\n```\n<script src=\"//cdn.rawgit.com/dcodeIO/protobuf.js/6.X.X/dist/protobuf.js\"></script>\n```\n\nProduction:\n\n```\n<script src=\"//cdn.rawgit.com/dcodeIO/protobuf.js/6.X.X/dist/protobuf.min.js\"></script>\n```\n\n**Remember** to replace the version tag with the exact [release](https://github.com/dcodeIO/protobuf.js/tags) your project depends upon.\n\nThe library supports CommonJS and AMD loaders and also exports globally as `protobuf`.\n\n### Distributions\n\nWhere bundle size is a factor, there are additional stripped-down versions of the [full library][dist-full] (~19kb gzipped) available that exclude certain functionality:\n\n* When working with JSON descriptors (i.e. generated by [pbjs](#pbjs-for-javascript)) and/or reflection only, see the [light library][dist-light] (~16kb gzipped) that excludes the parser. CommonJS entry point is:\n\n  ```js\n  var protobuf = require(\"protobufjs/light\");\n  ```\n\n* When working with statically generated code only, see the [minimal library][dist-minimal] (~6.5kb gzipped) that also excludes reflection. CommonJS entry point is:\n\n  ```js\n  var protobuf = require(\"protobufjs/minimal\");\n  ```\n\n[dist-full]: https://github.com/dcodeIO/protobuf.js/tree/master/dist\n[dist-light]: https://github.com/dcodeIO/protobuf.js/tree/master/dist/light\n[dist-minimal]: https://github.com/dcodeIO/protobuf.js/tree/master/dist/minimal\n\nUsage\n-----\n\nBecause JavaScript is a dynamically typed language, protobuf.js introduces the concept of a **valid message** in order to provide the best possible [performance](#performance) (and, as a side product, proper typings):\n\n### Valid message\n\n> A valid message is an object (1) not missing any required fields and (2) exclusively composed of JS types understood by the wire format writer.\n\nThere are two possible types of valid messages and the encoder is able to work with both of these for convenience:\n\n* **Message instances** (explicit instances of message classes with default values on their prototype) always (have to) satisfy the requirements of a valid message by design and\n* **Plain JavaScript objects** that just so happen to be composed in a way satisfying the requirements of a valid message as well.\n\nIn a nutshell, the wire format writer understands the following types:\n\n| Field type | Expected JS type (create, encode) | Conversion (fromObject)\n|------------|-----------------------------------|------------------------\n| s-/u-/int32<br />s-/fixed32 | `number` (32 bit integer) | <code>value &#124; 0</code> if signed<br />`value >>> 0` if unsigned\n| s-/u-/int64<br />s-/fixed64 | `Long`-like (optimal)<br />`number` (53 bit integer) | `Long.fromValue(value)` with long.js<br />`parseInt(value, 10)` otherwise\n| float<br />double | `number` | `Number(value)`\n| bool | `boolean` | `Boolean(value)`\n| string | `string` | `String(value)`\n| bytes | `Uint8Array` (optimal)<br />`Buffer` (optimal under node)<br />`Array.<number>` (8 bit integers) | `base64.decode(value)` if a `string`<br />`Object` with non-zero `.length` is assumed to be buffer-like\n| enum | `number` (32 bit integer) | Looks up the numeric id if a `string`\n| message | Valid message | `Message.fromObject(value)`\n\n* Explicit `undefined` and `null` are considered as not set if the field is optional.\n* Repeated fields are `Array.<T>`.\n* Map fields are `Object.<string,T>` with the key being the string representation of the respective value or an 8 characters long binary hash string for `Long`-likes.\n* Types marked as *optimal* provide the best performance because no conversion step (i.e. number to low and high bits or base64 string to buffer) is required.\n\n### Toolset\n\nWith that in mind and again for performance reasons, each message class provides a distinct set of methods with each method doing just one thing. This avoids unnecessary assertions / redundant operations where performance is a concern but also forces a user to perform verification (of plain JavaScript objects that *might* just so happen to be a valid message) explicitly where necessary - for example when dealing with user input.\n\n**Note** that `Message` below refers to any message class.\n\n* **Message.verify**(message: `Object`): `null|string`<br />\n  verifies that a **plain JavaScript object** satisfies the requirements of a valid message and thus can be encoded without issues. Instead of throwing, it returns the error message as a string, if any.\n\n  ```js\n  var payload = \"invalid (not an object)\";\n  var err = AwesomeMessage.verify(payload);\n  if (err)\n    throw Error(err);\n  ```\n\n* **Message.encode**(message: `Message|Object` [, writer: `Writer`]): `Writer`<br />\n  encodes a **message instance** or valid **plain JavaScript object**. This method does not implicitly verify the message and it's up to the user to make sure that the payload is a valid message.\n\n  ```js\n  var buffer = AwesomeMessage.encode(message).finish();\n  ```\n\n* **Message.encodeDelimited**(message: `Message|Object` [, writer: `Writer`]): `Writer`<br />\n  works like `Message.encode` but additionally prepends the length of the message as a varint.\n\n* **Message.decode**(reader: `Reader|Uint8Array`): `Message`<br />\n  decodes a buffer to a **message instance**. If required fields are missing, it throws a `util.ProtocolError` with an `instance` property set to the so far decoded message. If the wire format is invalid, it throws an `Error`.\n\n  ```js\n  try {\n    var decodedMessage = AwesomeMessage.decode(buffer);\n  } catch (e) {\n      if (e instanceof protobuf.util.ProtocolError) {\n        // e.instance holds the so far decoded message with missing required fields\n      } else {\n        // wire format is invalid\n      }\n  }\n  ```\n\n* **Message.decodeDelimited**(reader: `Reader|Uint8Array`): `Message`<br />\n  works like `Message.decode` but additionally reads the length of the message prepended as a varint.\n\n* **Message.create**(properties: `Object`): `Message`<br />\n  creates a new **message instance** from a set of properties that satisfy the requirements of a valid message. Where applicable, it is recommended to prefer `Message.create` over `Message.fromObject` because it doesn't perform possibly redundant conversion.\n\n  ```js\n  var message = AwesomeMessage.create({ awesomeField: \"AwesomeString\" });\n  ```\n\n* **Message.fromObject**(object: `Object`): `Message`<br />\n  converts any non-valid **plain JavaScript object** to a **message instance** using the conversion steps outlined within the table above.\n\n  ```js\n  var message = AwesomeMessage.fromObject({ awesomeField: 42 });\n  // converts awesomeField to a string\n  ```\n\n* **Message.toObject**(message: `Message` [, options: `ConversionOptions`]): `Object`<br />\n  converts a **message instance** to an arbitrary **plain JavaScript object** for interoperability with other libraries or storage. The resulting plain JavaScript object *might* still satisfy the requirements of a valid message depending on the actual conversion options specified, but most of the time it does not.\n\n  ```js\n  var object = AwesomeMessage.toObject(message, {\n    enums: String,  // enums as string names\n    longs: String,  // longs as strings (requires long.js)\n    bytes: String,  // bytes as base64 encoded strings\n    defaults: true, // includes default values\n    arrays: true,   // populates empty arrays (repeated fields) even if defaults=false\n    objects: true,  // populates empty objects (map fields) even if defaults=false\n    oneofs: true    // includes virtual oneof fields set to the present field's name\n  });\n  ```\n\nFor reference, the following diagram aims to display relationships between the different methods and the concept of a valid message:\n\n<p align=\"center\"><img alt=\"Toolset Diagram\" src=\"http://dcode.io/protobuf.js/toolset.svg\" /></p>\n\n> In other words: `verify` indicates that calling `create` or `encode` directly on the plain object will [result in a valid message respectively] succeed. `fromObject`, on the other hand, does conversion from a broader range of plain objects to create valid messages. ([ref](https://github.com/dcodeIO/protobuf.js/issues/748#issuecomment-*********))\n\nExamples\n--------\n\n### Using .proto files\n\nIt is possible to load existing .proto files using the full library, which parses and compiles the definitions to ready to use (reflection-based) message classes:\n\n```protobuf\n// awesome.proto\npackage awesomepackage;\nsyntax = \"proto3\";\n\nmessage AwesomeMessage {\n    string awesome_field = 1; // becomes awesomeField\n}\n```\n\n```js\nprotobuf.load(\"awesome.proto\", function(err, root) {\n    if (err)\n        throw err;\n\n    // Obtain a message type\n    var AwesomeMessage = root.lookupType(\"awesomepackage.AwesomeMessage\");\n\n    // Exemplary payload\n    var payload = { awesomeField: \"AwesomeString\" };\n\n    // Verify the payload if necessary (i.e. when possibly incomplete or invalid)\n    var errMsg = AwesomeMessage.verify(payload);\n    if (errMsg)\n        throw Error(errMsg);\n\n    // Create a new message\n    var message = AwesomeMessage.create(payload); // or use .fromObject if conversion is necessary\n\n    // Encode a message to an Uint8Array (browser) or Buffer (node)\n    var buffer = AwesomeMessage.encode(message).finish();\n    // ... do something with buffer\n\n    // Decode an Uint8Array (browser) or Buffer (node) to a message\n    var message = AwesomeMessage.decode(buffer);\n    // ... do something with message\n\n    // If the application uses length-delimited buffers, there is also encodeDelimited and decodeDelimited.\n\n    // Maybe convert the message back to a plain object\n    var object = AwesomeMessage.toObject(message, {\n        longs: String,\n        enums: String,\n        bytes: String,\n        // see ConversionOptions\n    });\n});\n```\n\nAdditionally, promise syntax can be used by omitting the callback, if preferred:\n\n```js\nprotobuf.load(\"awesome.proto\")\n    .then(function(root) {\n       ...\n    });\n```\n\n### Using JSON descriptors\n\nThe library utilizes JSON descriptors that are equivalent to a .proto definition. For example, the following is identical to the .proto definition seen above:\n\n```json\n// awesome.json\n{\n  \"nested\": {\n    \"AwesomeMessage\": {\n      \"fields\": {\n        \"awesomeField\": {\n          \"type\": \"string\",\n          \"id\": 1\n        }\n      }\n    }\n  }\n}\n```\n\nJSON descriptors closely resemble the internal reflection structure:\n\n| Type (T)           | Extends            | Type-specific properties\n|--------------------|--------------------|-------------------------\n| *ReflectionObject* |                    | options\n| *Namespace*        | *ReflectionObject* | nested\n| Root               | *Namespace*        | **nested**\n| Type               | *Namespace*        | **fields**\n| Enum               | *ReflectionObject* | **values**\n| Field              | *ReflectionObject* | rule, **type**, **id**\n| MapField           | Field              | **keyType**\n| OneOf              | *ReflectionObject* | **oneof** (array of field names)\n| Service            | *Namespace*        | **methods**\n| Method             | *ReflectionObject* | type, **requestType**, **responseType**, requestStream, responseStream\n\n* **Bold properties** are required. *Italic types* are abstract.\n* `T.fromJSON(name, json)` creates the respective reflection object from a JSON descriptor\n* `T#toJSON()` creates a JSON descriptor from the respective reflection object (its name is used as the key within the parent)\n\nExclusively using JSON descriptors instead of .proto files enables the use of just the light library (the parser isn't required in this case).\n\nA JSON descriptor can either be loaded the usual way:\n\n```js\nprotobuf.load(\"awesome.json\", function(err, root) {\n    if (err) throw err;\n\n    // Continue at \"Obtain a message type\" above\n});\n```\n\nOr it can be loaded inline:\n\n```js\nvar jsonDescriptor = require(\"./awesome.json\"); // exemplary for node\n\nvar root = protobuf.Root.fromJSON(jsonDescriptor);\n\n// Continue at \"Obtain a message type\" above\n```\n\n### Using reflection only\n\nBoth the full and the light library include full reflection support. One could, for example, define the .proto definitions seen in the examples above using just reflection:\n\n```js\n...\nvar Root  = protobuf.Root,\n    Type  = protobuf.Type,\n    Field = protobuf.Field;\n\nvar AwesomeMessage = new Type(\"AwesomeMessage\").add(new Field(\"awesomeField\", 1, \"string\"));\n\nvar root = new Root().define(\"awesomepackage\").add(AwesomeMessage);\n\n// Continue at \"Create a new message\" above\n...\n```\n\nDetailed information on the reflection structure is available within the [API documentation](#additional-documentation).\n\n### Using custom classes\n\nMessage classes can also be extended with custom functionality and it is also possible to register a custom constructor with a reflected message type:\n\n```js\n...\n\n// Define a custom constructor\nfunction AwesomeMessage(properties) {\n    // custom initialization code\n    ...\n}\n\n// Register the custom constructor with its reflected type (*)\nroot.lookupType(\"awesomepackage.AwesomeMessage\").ctor = AwesomeMessage;\n\n// Define custom functionality\nAwesomeMessage.customStaticMethod = function() { ... };\nAwesomeMessage.prototype.customInstanceMethod = function() { ... };\n\n// Continue at \"Create a new message\" above\n```\n\n(*) Besides referencing its reflected type through `AwesomeMessage.$type` and `AwesomeMesage#$type`, the respective custom class is automatically populated with:\n\n* `AwesomeMessage.create`\n* `AwesomeMessage.encode` and `AwesomeMessage.encodeDelimited`\n* `AwesomeMessage.decode` and `AwesomeMessage.decodeDelimited`\n* `AwesomeMessage.verify`\n* `AwesomeMessage.fromObject`, `AwesomeMessage.toObject`, `AwesomeMessage#toObject` and `AwesomeMessage#toJSON`\n\nAfterwards, decoded messages of this type are `instanceof AwesomeMessage`.\n\nAlternatively, it is also possible to reuse and extend the internal constructor if custom initialization code is not required:\n\n```js\n...\n\n// Reuse the internal constructor\nvar AwesomeMessage = root.lookupType(\"awesomepackage.AwesomeMessage\").ctor;\n\n// Define custom functionality\nAwesomeMessage.customStaticMethod = function() { ... };\nAwesomeMessage.prototype.customInstanceMethod = function() { ... };\n\n// Continue at \"Create a new message\" above\n```\n\n### Using services\n\nThe library also supports consuming services but it doesn't make any assumptions about the actual transport channel. Instead, a user must provide a suitable RPC implementation, which is an asynchronous function that takes the reflected service method, the binary request and a node-style callback as its parameters:\n\n```js\nfunction rpcImpl(method, requestData, callback) {\n    // perform the request using an HTTP request or a WebSocket for example\n    var responseData = ...;\n    // and call the callback with the binary response afterwards:\n    callback(null, responseData);\n}\n```\n\nExample:\n\n```protobuf\n// greeter.proto\nsyntax = \"proto3\";\n\nservice Greeter {\n    rpc SayHello (HelloRequest) returns (HelloReply) {}\n}\n\nmessage HelloRequest {\n    string name = 1;\n}\n\nmessage HelloReply {\n    string message = 1;\n}\n```\n\n```js\n...\nvar Greeter = root.lookup(\"Greeter\");\nvar greeter = Greeter.create(/* see above */ rpcImpl, /* request delimited? */ false, /* response delimited? */ false);\n\ngreeter.sayHello({ name: 'you' }, function(err, response) {\n    console.log('Greeting:', response.message);\n});\n```\n\nServices also support promises:\n\n```js\ngreeter.sayHello({ name: 'you' })\n    .then(function(response) {\n        console.log('Greeting:', response.message);\n    });\n```\n\nThere is also an [example for streaming RPC](https://github.com/dcodeIO/protobuf.js/blob/master/examples/streaming-rpc.js).\n\nNote that the service API is meant for clients. Implementing a server-side endpoint pretty much always requires transport channel (i.e. http, websocket, etc.) specific code with the only common denominator being that it decodes and encodes messages.\n\n### Usage with TypeScript\n\nThe library ships with its own [type definitions](https://github.com/dcodeIO/protobuf.js/blob/master/index.d.ts) and modern editors like [Visual Studio Code](https://code.visualstudio.com/) will automatically detect and use them for code completion.\n\nThe npm package depends on [@types/node](https://www.npmjs.com/package/@types/node) because of `Buffer` and [@types/long](https://www.npmjs.com/package/@types/long) because of `Long`. If you are not building for node and/or not using long.js, it should be safe to exclude them manually.\n\n#### Using the JS API\n\nThe API shown above works pretty much the same with TypeScript. However, because everything is typed, accessing fields on instances of dynamically generated message classes requires either using bracket-notation (i.e. `message[\"awesomeField\"]`) or explicit casts. Alternatively, it is possible to use a [typings file generated for its static counterpart](#pbts-for-typescript).\n\n```ts\nimport { load } from \"protobufjs\"; // respectively \"./node_modules/protobufjs\"\n\nload(\"awesome.proto\", function(err, root) {\n  if (err)\n    throw err;\n\n  // example code\n  const AwesomeMessage = root.lookupType(\"awesomepackage.AwesomeMessage\");\n\n  let message = AwesomeMessage.create({ awesomeField: \"hello\" });\n  console.log(`message = ${JSON.stringify(message)}`);\n\n  let buffer = AwesomeMessage.encode(message).finish();\n  console.log(`buffer = ${Array.prototype.toString.call(buffer)}`);\n\n  let decoded = AwesomeMessage.decode(buffer);\n  console.log(`decoded = ${JSON.stringify(decoded)}`);\n});\n```\n\n#### Using generated static code\n\nIf you generated static code to `bundle.js` using the CLI and its type definitions to `bundle.d.ts`, then you can just do:\n\n```ts\nimport { AwesomeMessage } from \"./bundle.js\";\n\n// example code\nlet message = AwesomeMessage.create({ awesomeField: \"hello\" });\nlet buffer  = AwesomeMessage.encode(message).finish();\nlet decoded = AwesomeMessage.decode(buffer);\n```\n\n#### Using decorators\n\nThe library also includes an early implementation of [decorators](https://www.typescriptlang.org/docs/handbook/decorators.html).\n\n**Note** that decorators are an experimental feature in TypeScript and that declaration order is important depending on the JS target. For example, `@Field.d(2, AwesomeArrayMessage)` requires that `AwesomeArrayMessage` has been defined earlier when targeting `ES5`.\n\n```ts\nimport { Message, Type, Field, OneOf } from \"protobufjs/light\"; // respectively \"./node_modules/protobufjs/light.js\"\n\nexport class AwesomeSubMessage extends Message<AwesomeSubMessage> {\n\n  @Field.d(1, \"string\")\n  public awesomeString: string;\n\n}\n\nexport enum AwesomeEnum {\n  ONE = 1,\n  TWO = 2\n}\n\n@Type.d(\"SuperAwesomeMessage\")\nexport class AwesomeMessage extends Message<AwesomeMessage> {\n\n  @Field.d(1, \"string\", \"optional\", \"awesome default string\")\n  public awesomeField: string;\n\n  @Field.d(2, AwesomeSubMessage)\n  public awesomeSubMessage: AwesomeSubMessage;\n\n  @Field.d(3, AwesomeEnum, \"optional\", AwesomeEnum.ONE)\n  public awesomeEnum: AwesomeEnum;\n\n  @OneOf.d(\"awesomeSubMessage\", \"awesomeEnum\")\n  public which: string;\n\n}\n\n// example code\nlet message = new AwesomeMessage({ awesomeField: \"hello\" });\nlet buffer  = AwesomeMessage.encode(message).finish();\nlet decoded = AwesomeMessage.decode(buffer);\n```\n\nSupported decorators are:\n\n* **Type.d(typeName?: `string`)** &nbsp; *(optional)*<br />\n  annotates a class as a protobuf message type. If `typeName` is not specified, the constructor's runtime function name is used for the reflected type.\n\n* **Field.d&lt;T>(fieldId: `number`, fieldType: `string | Constructor<T>`, fieldRule?: `\"optional\" | \"required\" | \"repeated\"`, defaultValue?: `T`)**<br />\n  annotates a property as a protobuf field with the specified id and protobuf type.\n\n* **MapField.d&lt;T extends { [key: string]: any }>(fieldId: `number`, fieldKeyType: `string`, fieldValueType. `string | Constructor<{}>`)**<br />\n  annotates a property as a protobuf map field with the specified id, protobuf key and value type.\n\n* **OneOf.d&lt;T extends string>(...fieldNames: `string[]`)**<br />\n  annotates a property as a protobuf oneof covering the specified fields.\n\nOther notes:\n\n* Decorated types reside in `protobuf.roots[\"decorated\"]` using a flat structure, so no duplicate names.\n* Enums are copied to a reflected enum with a generic name on decorator evaluation because referenced enum objects have no runtime name the decorator could use.\n* Default values must be specified as arguments to the decorator instead of using a property initializer for proper prototype behavior.\n* Property names on decorated classes must not be renamed on compile time (i.e. by a minifier) because decorators just receive the original field name as a string.\n\n**ProTip!** Not as pretty, but you can [use decorators in plain JavaScript](https://github.com/dcodeIO/protobuf.js/blob/master/examples/js-decorators.js) as well.\n\nCommand line\n------------\n\n**Note** that moving the CLI to [its own package](./cli) is a work in progress. At the moment, it's still part of the main package.\n\nThe command line interface (CLI) can be used to translate between file formats and to generate static code as well as TypeScript definitions.\n\n### pbjs for JavaScript\n\n```\nTranslates between file formats and generates static code.\n\n  -t, --target     Specifies the target format. Also accepts a path to require a custom target.\n\n                   json          JSON representation\n                   json-module   JSON representation as a module\n                   proto2        Protocol Buffers, Version 2\n                   proto3        Protocol Buffers, Version 3\n                   static        Static code without reflection (non-functional on its own)\n                   static-module Static code without reflection as a module\n\n  -p, --path       Adds a directory to the include path.\n\n  -o, --out        Saves to a file instead of writing to stdout.\n\n  --sparse         Exports only those types referenced from a main file (experimental).\n\n  Module targets only:\n\n  -w, --wrap       Specifies the wrapper to use. Also accepts a path to require a custom wrapper.\n\n                   default   Default wrapper supporting both CommonJS and AMD\n                   commonjs  CommonJS wrapper\n                   amd       AMD wrapper\n                   es6       ES6 wrapper (implies --es6)\n                   closure   A closure adding to protobuf.roots where protobuf is a global\n\n  -r, --root       Specifies an alternative protobuf.roots name.\n\n  -l, --lint       Linter configuration. Defaults to protobuf.js-compatible rules:\n\n                   eslint-disable block-scoped-var, no-redeclare, no-control-regex, no-prototype-builtins\n\n  --es6            Enables ES6 syntax (const/let instead of var)\n\n  Proto sources only:\n\n  --keep-case      Keeps field casing instead of converting to camel case.\n\n  Static targets only:\n\n  --no-create      Does not generate create functions used for reflection compatibility.\n  --no-encode      Does not generate encode functions.\n  --no-decode      Does not generate decode functions.\n  --no-verify      Does not generate verify functions.\n  --no-convert     Does not generate convert functions like from/toObject\n  --no-delimited   Does not generate delimited encode/decode functions.\n  --no-beautify    Does not beautify generated code.\n  --no-comments    Does not output any JSDoc comments.\n\n  --force-long     Enfores the use of 'Long' for s-/u-/int64 and s-/fixed64 fields.\n  --force-message  Enfores the use of message instances instead of plain objects.\n\nusage: pbjs [options] file1.proto file2.json ...  (or pipe)  other | pbjs [options] -\n```\n\nFor production environments it is recommended to bundle all your .proto files to a single .json file, which minimizes the number of network requests and avoids any parser overhead (hint: works with just the **light** library):\n\n```\n$> pbjs -t json file1.proto file2.proto > bundle.json\n```\n\nNow, either include this file in your final bundle:\n\n```js\nvar root = protobuf.Root.fromJSON(require(\"./bundle.json\"));\n```\n\nor load it the usual way:\n\n```js\nprotobuf.load(\"bundle.json\", function(err, root) {\n    ...\n});\n```\n\nGenerated static code, on the other hand, works with just the **minimal** library. For example\n\n```\n$> pbjs -t static-module -w commonjs -o compiled.js file1.proto file2.proto\n```\n\nwill generate static code for definitions within `file1.proto` and `file2.proto` to a CommonJS module `compiled.js`.\n\n**ProTip!** Documenting your .proto files with `/** ... */`-blocks or (trailing) `/// ...` lines translates to generated static code.\n\n\n### pbts for TypeScript\n\n```\nGenerates TypeScript definitions from annotated JavaScript files.\n\n  -o, --out       Saves to a file instead of writing to stdout.\n\n  -g, --global    Name of the global object in browser environments, if any.\n\n  --no-comments   Does not output any JSDoc comments.\n\n  Internal flags:\n\n  -n, --name      Wraps everything in a module of the specified name.\n\n  -m, --main      Whether building the main library without any imports.\n\nusage: pbts [options] file1.js file2.js ...  (or)  other | pbts [options] -\n```\n\nPicking up on the example above, the following not only generates static code to a CommonJS module `compiled.js` but also its respective TypeScript definitions to `compiled.d.ts`:\n\n```\n$> pbjs -t static-module -w commonjs -o compiled.js file1.proto file2.proto\n$> pbts -o compiled.d.ts compiled.js\n```\n\nAdditionally, TypeScript definitions of static modules are compatible with their reflection-based counterparts (i.e. as exported by JSON modules), as long as the following conditions are met:\n\n1. Instead of using `new SomeMessage(...)`, always use `SomeMessage.create(...)` because reflection objects do not provide a constructor.\n2. Types, services and enums must start with an uppercase letter to become available as properties of the reflected types as well (i.e. to be able to use `MyMessage.MyEnum` instead of `root.lookup(\"MyMessage.MyEnum\")`).\n\nFor example, the following generates a JSON module `bundle.js` and a `bundle.d.ts`, but no static code:\n\n```\n$> pbjs -t json-module -w commonjs -o bundle.js file1.proto file2.proto\n$> pbjs -t static-module file1.proto file2.proto | pbts -o bundle.d.ts -\n```\n\n### Reflection vs. static code\n\nWhile using .proto files directly requires the full library respectively pure reflection/JSON the light library, pretty much all code but the relatively short descriptors is shared.\n\nStatic code, on the other hand, requires just the minimal library, but generates additional source code without any reflection features. This also implies that there is a break-even point where statically generated code becomes larger than descriptor-based code once the amount of code generated exceeds the size of the full respectively light library.\n\nThere is no significant difference performance-wise as the code generated statically is pretty much the same as generated at runtime and both are largely interchangeable as seen in the previous section.\n\n| Source | Library | Advantages | Tradeoffs\n|--------|---------|------------|-----------\n| .proto | full    | Easily editable<br />Interoperability with other libraries<br />No compile step | Some parsing and possibly network overhead\n| JSON   | light   | Easily editable<br />No parsing overhead<br />Single bundle (no network overhead) | protobuf.js specific<br />Has a compile step\n| static | minimal | Works where `eval` access is restricted<br />Fully documented<br />Small footprint for small protos | Can be hard to edit<br />No reflection<br />Has a compile step\n\n### Command line API\n\nBoth utilities can be used programmatically by providing command line arguments and a callback to their respective `main` functions:\n\n```js\nvar pbjs = require(\"protobufjs/cli/pbjs\"); // or require(\"protobufjs/cli\").pbjs / .pbts\n\npbjs.main([ \"--target\", \"json-module\", \"path/to/myproto.proto\" ], function(err, output) {\n    if (err)\n        throw err;\n    // do something with output\n});\n```\n\nAdditional documentation\n------------------------\n\n#### Protocol Buffers\n* [Google's Developer Guide](https://developers.google.com/protocol-buffers/docs/overview)\n\n#### protobuf.js\n* [API Documentation](http://dcode.io/protobuf.js)\n* [CHANGELOG](https://github.com/dcodeIO/protobuf.js/blob/master/CHANGELOG.md)\n* [Frequently asked questions](https://github.com/dcodeIO/protobuf.js/wiki) on our wiki\n\n#### Community\n* [Questions and answers](http://stackoverflow.com/search?tab=newest&q=protobuf.js) on StackOverflow\n\nPerformance\n-----------\nThe package includes a benchmark that compares protobuf.js performance to native JSON (as far as this is possible) and [Google's JS implementation](https://github.com/google/protobuf/tree/master/js). On an i7-2600K running node 6.9.1 it yields:\n\n```\nbenchmarking encoding performance ...\n\nprotobuf.js (reflect) x 541,707 ops/sec ±1.13% (87 runs sampled)\nprotobuf.js (static) x 548,134 ops/sec ±1.38% (89 runs sampled)\nJSON (string) x 318,076 ops/sec ±0.63% (93 runs sampled)\nJSON (buffer) x 179,165 ops/sec ±2.26% (91 runs sampled)\ngoogle-protobuf x 74,406 ops/sec ±0.85% (86 runs sampled)\n\n   protobuf.js (static) was fastest\n  protobuf.js (reflect) was 0.9% ops/sec slower (factor 1.0)\n          JSON (string) was 41.5% ops/sec slower (factor 1.7)\n          JSON (buffer) was 67.6% ops/sec slower (factor 3.1)\n        google-protobuf was 86.4% ops/sec slower (factor 7.3)\n\nbenchmarking decoding performance ...\n\nprotobuf.js (reflect) x 1,383,981 ops/sec ±0.88% (93 runs sampled)\nprotobuf.js (static) x 1,378,925 ops/sec ±0.81% (93 runs sampled)\nJSON (string) x 302,444 ops/sec ±0.81% (93 runs sampled)\nJSON (buffer) x 264,882 ops/sec ±0.81% (93 runs sampled)\ngoogle-protobuf x 179,180 ops/sec ±0.64% (94 runs sampled)\n\n  protobuf.js (reflect) was fastest\n   protobuf.js (static) was 0.3% ops/sec slower (factor 1.0)\n          JSON (string) was 78.1% ops/sec slower (factor 4.6)\n          JSON (buffer) was 80.8% ops/sec slower (factor 5.2)\n        google-protobuf was 87.0% ops/sec slower (factor 7.7)\n\nbenchmarking combined performance ...\n\nprotobuf.js (reflect) x 275,900 ops/sec ±0.78% (90 runs sampled)\nprotobuf.js (static) x 290,096 ops/sec ±0.96% (90 runs sampled)\nJSON (string) x 129,381 ops/sec ±0.77% (90 runs sampled)\nJSON (buffer) x 91,051 ops/sec ±0.94% (90 runs sampled)\ngoogle-protobuf x 42,050 ops/sec ±0.85% (91 runs sampled)\n\n   protobuf.js (static) was fastest\n  protobuf.js (reflect) was 4.7% ops/sec slower (factor 1.0)\n          JSON (string) was 55.3% ops/sec slower (factor 2.2)\n          JSON (buffer) was 68.6% ops/sec slower (factor 3.2)\n        google-protobuf was 85.5% ops/sec slower (factor 6.9)\n```\n\nThese results are achieved by\n\n* generating type-specific encoders, decoders, verifiers and converters at runtime\n* configuring the reader/writer interface according to the environment\n* using node-specific functionality where beneficial and, of course\n* avoiding unnecessary operations through splitting up [the toolset](#toolset).\n\nYou can also run [the benchmark](https://github.com/dcodeIO/protobuf.js/blob/master/bench/index.js) ...\n\n```\n$> npm run bench\n```\n\nand [the profiler](https://github.com/dcodeIO/protobuf.js/blob/master/bench/prof.js) yourself (the latter requires a recent version of node):\n\n```\n$> npm run prof <encode|decode|encode-browser|decode-browser> [iterations=10000000]\n```\n\nNote that as of this writing, the benchmark suite performs significantly slower on node 7.2.0 compared to 6.9.1 because moths.\n\nCompatibility\n-------------\n\n* Works in all modern and not-so-modern browsers except IE8.\n* Because the internals of this package do not rely on `google/protobuf/descriptor.proto`, options are parsed and presented literally.\n* If typed arrays are not supported by the environment, plain arrays will be used instead.\n* Support for pre-ES5 environments (except IE8) can be achieved by [using a polyfill](https://github.com/dcodeIO/protobuf.js/blob/master/scripts/polyfill.js).\n* Support for [Content Security Policy](https://w3c.github.io/webappsec-csp/)-restricted environments (like Chrome extensions without [unsafe-eval](https://developer.chrome.com/extensions/contentSecurityPolicy#relaxing-eval)) can be achieved by generating and using static code instead.\n* If a proper way to work with 64 bit values (uint64, int64 etc.) is required, just install [long.js](https://github.com/dcodeIO/long.js) alongside this library. All 64 bit numbers will then be returned as a `Long` instance instead of a possibly unsafe JavaScript number ([see](https://github.com/dcodeIO/long.js)).\n* For descriptor.proto interoperability, see [ext/descriptor](https://github.com/dcodeIO/protobuf.js/tree/master/ext/descriptor)\n\nBuilding\n--------\n\nTo build the library or its components yourself, clone it from GitHub and install the development dependencies:\n\n```\n$> git clone https://github.com/dcodeIO/protobuf.js.git\n$> cd protobuf.js\n$> npm install\n```\n\nBuilding the respective development and production versions with their respective source maps to `dist/`:\n\n```\n$> npm run build\n```\n\nBuilding the documentation to `docs/`:\n\n```\n$> npm run docs\n```\n\nBuilding the TypeScript definition to `index.d.ts`:\n\n```\n$> npm run types\n```\n\n### Browserify integration\n\nBy default, protobuf.js integrates into any browserify build-process without requiring any optional modules. Hence:\n\n* If int64 support is required, explicitly require the `long` module somewhere in your project as it will be excluded otherwise. This assumes that a global `require` function is present that protobuf.js can call to obtain the long module.\n\n  If there is no global `require` function present after bundling, it's also possible to assign the long module programmatically:\n\n  ```js\n  var Long = ...;\n\n  protobuf.util.Long = Long;\n  protobuf.configure();\n  ```\n\n* If you have any special requirements, there is [the bundler](https://github.com/dcodeIO/protobuf.js/blob/master/scripts/bundle.js) for reference.\n\n**License:** [BSD 3-Clause License](https://opensource.org/licenses/BSD-3-Clause)\n", "licenseText": "This license applies to all parts of protobuf.js except those files\neither explicitly including or referencing a different license or\nlocated in a directory containing a different LICENSE file.\n\n---\n\nCopyright (c) 2016, <PERSON>  All rights reserved.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are\nmet:\n\n* Redistributions of source code must retain the above copyright\n  notice, this list of conditions and the following disclaimer.\n* Redistributions in binary form must reproduce the above copyright\n  notice, this list of conditions and the following disclaimer in the\n  documentation and/or other materials provided with the distribution.\n* Neither the name of its author, nor the names of its contributors\n  may be used to endorse or promote products derived from this software\n  without specific prior written permission.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n\"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\nLIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\nA PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\nOWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\nSPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\nLIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\nDATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\nTHEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\nOF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\n---\n\nCode generated by the command line utilities is owned by the owner\nof the input file used when generating it. This code is not\nstandalone and requires a support library to be linked with it. This\nsupport library is itself covered by the above license.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@citizenfx/protobufjs/-/protobufjs-6.8.8.tgz#d12edcada06182f3785dea1d35eebf0ebe4fd2c3", "type": "tarball", "reference": "https://registry.yarnpkg.com/@citizenfx/protobufjs/-/protobufjs-6.8.8.tgz", "hash": "d12edcada06182f3785dea1d35eebf0ebe4fd2c3", "integrity": "sha512-RBJvHPWNwguEPxV+ALbCZBXAEQf2byP5KtYrYl36Mbb0+Ch7MxpOm+764S+YqYWj/A/iNSW3jXglfJ9hlxjBLA==", "registry": "npm", "packageName": "@citizenfx/protobufjs", "cacheIntegrity": "sha512-RBJvHPWNwguEPxV+ALbCZBXAEQf2byP5KtYrYl36Mbb0+Ch7MxpOm+764S+YqYWj/A/iNSW3jXglfJ9hlxjBLA== sha1-0S7craBhgvN4XeodNe6/Dr5P0sM="}, "registry": "npm", "hash": "d12edcada06182f3785dea1d35eebf0ebe4fd2c3"}