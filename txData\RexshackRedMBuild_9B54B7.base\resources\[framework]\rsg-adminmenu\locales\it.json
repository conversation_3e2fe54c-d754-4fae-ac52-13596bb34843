{"cl_client_0": "<PERSON><PERSON>", "cl_client_1": "visualizza opzioni admin", "cl_client_2": "Opzioni Giocatore", "cl_client_3": "visualizza opzioni giocatore", "cl_client_88": "Finanze Giocatore", "cl_client_89": "gestisci finanze del giocatore", "cl_client_4": "Opzioni Troll", "cl_client_5": "visualizza opzioni troll", "cl_client_6": "Gestisci Server", "cl_client_7": "visualizza opzioni server", "cl_client_8": "Opzioni Sviluppatore", "cl_client_9": "visualizza opzioni sviluppatore", "cl_client_10": "Menu Opzioni Admin", "cl_client_11": "Teletrasporto al Marcatore", "cl_client_12": "devi avere un marcatore impostato prima di farlo", "cl_client_13": "Auto-Revive", "cl_client_14": "<PERSON><PERSON><PERSON><PERSON> dalla morte", "cl_client_15": "Diventa Invisibile", "cl_client_16": "attiva/disattiva invisibilità", "cl_client_146": "Mostra ID", "cl_client_147": "attiva/disattiva ID dei giocatori", "cl_client_17": "Modalità Dio", "cl_client_18": "attiva/disattiva modalità dio", "cl_client_19": "ID:", "cl_client_21": "Menu Giocatori", "cl_client_137": "Info G<PERSON>ore", "cl_client_138": "ottieni informazioni del giocatore", "cl_client_22": "<PERSON><PERSON><PERSON>", "cl_client_23": "rianima questo gio<PERSON>ore", "cl_client_130": "<PERSON>", "cl_client_131": "dona un oggetto a un giocatore", "cl_client_24": "Inventario Giocatore", "cl_client_25": "apri l'inventario del giocatore, premi [I] quando aperto", "cl_client_26": "<PERSON><PERSON>cia G<PERSON>ore", "cl_client_27": "espelli un giocatore dal server con un motivo", "cl_client_28": "Banna Giocatore", "cl_client_29": "banna un giocatore dal server con un motivo", "cl_client_30": "Teletrasporto da Giocatore", "cl_client_31": "vai da un giocatore", "cl_client_32": "Teletrasporta Giocatore", "cl_client_33": "porta un giocatore da te", "cl_client_34": "Blocca Giocatore", "cl_client_35": "attiva/disattiva blocco giocatore", "cl_client_36": "Osserva Giocatore", "cl_client_37": "attiva/disattiva osservazione di un giocatore", "cl_client_38": "Menu Opzioni Server", "cl_client_39": "Meteo Server", "cl_client_40": "modifica il meteo del server", "cl_adminmenu": "Opzioni Admin", "cl_adminmenu_a": "modalità > INVISIBILE < attivata", "cl_client_42": "Invisibile Attivo", "cl_client_43": "come puoi vedere, sei invisibile!", "cl_client_44": "Invisibile Disattivato", "cl_client_45": "come puoi vedere, non sei invisibile!", "cl_client_46": "Modalità Dio Attiva", "cl_client_47": "modalità dio attivata!", "cl_adminmenu_b": "modalità > GODMODE < attivata", "cl_client_48": "Modalità Dio Disattiva", "cl_client_49": "modalità dio disattivata!", "cl_client_50": "<PERSON><PERSON>cia G<PERSON>ore", "cl_client_51": "Motivo", "cl_client_52": "Banna Giocatore", "cl_client_53": "Tipo di Ban", "cl_client_53_a": "Permanente", "cl_client_53_b": "Temporaneo", "cl_client_54": "Durata del Ban", "cl_client_55": "1 Ora", "cl_client_56": "6 Ore", "cl_client_57": "12 Ore", "cl_client_58": "1 Giorno", "cl_client_59": "3 Giorni", "cl_client_60": "1 Settimana", "cl_client_61": "1 Mese", "cl_client_62": "3 Mesi", "cl_client_63": "6 Mesi", "cl_client_64": "1 Anno", "cl_client_65": "Permanente", "cl_client_66": "Giocatore <PERSON>", "cl_client_67": "è stato bannato permanentemente", "cl_client_68": "ha un ban temporaneo impostato", "cl_client_132": "Dai <PERSON> al Giocatore", "cl_client_133": "Oggetto dell'Inventario", "cl_client_134": "Quantità", "cl_client_139": "Info Admin del Giocatore", "cl_client_140": "Nome", "cl_client_141": "Lavoro", "cl_client_142": "Grado del Lavoro", "cl_client_143": "<PERSON><PERSON><PERSON>", "cl_client_144": "Denaro Sporco", "cl_client_153": "Banca", "cl_client_148": "ValBank", "cl_client_149": "RhoBank", "cl_client_150": "BlkBank", "cl_client_151": "ArmBank", "cl_anim_test": "Tester di Animazioni", "cl_anim_dictionary": "animDictionary", "cl_anim_name": "animationName", "cl_anim_flag": "flag", "cl_anim_length": "durata : in millisecondi", "cl_coords_menu": "Copia Coordinate", "cl_coords_70": "Copia Vettore 2", "cl_coords_71": "copia coordinate vettore2", "cl_coords_72": "Copia Vettore 3", "cl_coords_73": "copia coordinate vettore3", "cl_coords_74": "Copia Vettore 4", "cl_coords_75": "copia coordinate vettore4", "cl_coords_76": "Copia Direzione", "cl_coords_77": "copia direzione", "cl_coords_print_list": "Stampa Lista Attiva", "cl_coords_print_list_a": "Abilita stampa delle coordinate in una lista", "cl_coords_print_full": "Stampa Lista Completa", "cl_coords_print_full_a": "Stampa la lista completa delle coordinate", "cl_coords_78": "Coordinate Copiate", "cl_coords_79": "coordinate vettore2 copiate negli appunti", "cl_coords_80": "coordinate vettore3 copiate negli appunti", "cl_coords_81": "coordinate vettore4 copiate negli appunti", "cl_coords_82": "Direzione Copiata", "cl_coords_83": "direzione copiata negli appunti", "cl_coords_printlist": "Stampa Lista", "cl_coords_printlist_a": "Stampa della lista abilitata. Le coordinate saranno aggiunte alla lista.", "cl_coords_printlist_b": "Lista copiata negli appunti", "cl_coords_printlist_c": "La lista è vuota", "cl_dev_menu": "<PERSON><PERSON>", "cl_dev_menu_a": "<PERSON><PERSON>", "cl_dev_menu_b": "genera un cavallo admin", "cl_dev_menu_c": "Menu Copia Coordinate", "cl_dev_menu_d": "copia coordinate negli appunti", "cl_dev_menu_e": "Tester di Animazioni", "cl_dev_menu_f": "testa animazioni", "cl_dev_menu_g": "<PERSON><PERSON>", "cl_dev_menu_h": "ottieni hash entità", "cl_dev_menu_i": "Mostra/Nascondi ID delle Porte", "cl_dev_menu_j": "usato per ottenere gli ID delle porte", "cl_dev_menu_l": "Spawn Ped", "cl_dev_menu_m": "usato per spawnare NPC/animali", "cl_dev_126": "<PERSON><PERSON>", "cl_dev_127": "<PERSON><PERSON><PERSON>", "cl_dev_128": "nome entità", "cl_dev_129": "esempio : PROVISION_ALLIGATOR_SKIN", "cl_dev_copy": "Hash Entità <PERSON>", "cl_dev_copy_a": "hash dell'entità di", "cl_dev_copy_b": "è stato copiato negli appunti", "cl_dev_spawnped": "Genera Ped/Animale", "cl_dev_spawnped1": "nome ped", "cl_dev_spawnped2": "esempio : mp_a_c_wolf_01", "cl_dev_spawnped3": "outfit", "cl_dev_spawnped4": "numero dell'outfit per ped/animale", "cl_dev_spawnped5": "distanza", "cl_dev_spawnped6": "distanza di spawn da te", "cl_dev_spawnped7": "blocca", "cl_dev_spawnped8": "blocca NPC/animale al momento dello spawn", "cl_dev_spawnped9": "Vero", "cl_dev_spawnped10": "<PERSON><PERSON><PERSON>", "cl_dev_spawnped11": "spawn morto", "cl_dev_spawnped12": "spawn NPC/animale morto", "cl_door_on": "ID Porte Attivi", "cl_door_off": "ID Porte Disattivi", "cl_door_id": "ID Porta", "cl_door_o": "<PERSON><PERSON><PERSON>", "cl_finan_menu": "Finanze Giocatore", "cl_finan_19": "ID:", "cl_finan_90": "Menu Opzioni Finanze", "cl_finan_122": "Banca : $", "cl_finan_122_a": "ValBank : $", "cl_finan_122_b": "RhoBank : $", "cl_finan_122_c": "BlkBank : $", "cl_finan_122_d": "ArmBank : $", "cl_finan_123": "Contanti : $", "cl_finan_123_a": "Denaro sporco : $", "cl_finan_119": "saldo bancario corrente del giocatore", "cl_finan_120": "saldo in contanti corrente del giocatore", "cl_finan_121": "saldo del denaro sporco corrente del giocatore", "cl_finan_91": "<PERSON>", "cl_finan_92": "dai denaro al giocatore", "cl_finan_93": "<PERSON><PERSON><PERSON><PERSON>", "cl_finan_94": "rimuovi denaro al giocatore", "cl_finan_95": "Tipo", "cl_finan_96": "scegli il tipo di denaro da dare al giocatore", "cl_finan_2": "Banca Std", "cl_finan_2a": "Banca Val", "cl_finan_2b": "Banca Rho", "cl_finan_2c": "Banca Blk", "cl_finan_2d": "Banca Arm", "cl_finan_3": "<PERSON><PERSON><PERSON>", "cl_finan_3a": "Denaro sporco", "cl_finan_97": "Importo", "cl_finan_98": "quanto vuoi dare?", "cl_finan_99": "scegli il tipo di denaro da rimuovere al giocatore", "cl_finan_115": "quanto vuoi rimuovere?", "cl_troll_19": "ID", "cl_troll_84": "Trolla Giocatore", "cl_troll_85": "Menu Opzioni Troll", "cl_troll_86": "Attacco Selvaggio", "cl_troll_87": "trolla il giocatore attivando un attacco selvaggio", "cl_troll_128": "Dai Fu<PERSON> al Giocatore", "cl_troll_129": "dai fuoco a un giocatore", "sv_a": "Giocatore <PERSON>", "sv_b": "%s è stato bannato da %s per %s", "sv_c": "sistema ti ha bannato per uso inappropriato", "sv_105": "Sei stato bannato permanentemente dal server per: Exploiting", "sv_100": "apri il menu admin (Solo Admin)", "sv_d": "Utilizzo Non Autorizzato del Menu Admin", "sv_e": "con ID cittadino di", "sv_f": "ha tentato di utilizzare il menu admin", "sv_101": "Non Consentito", "sv_102": "non ti è consentito farlo!", "sv_g": "Uso Non Autorizzato", "sv_h": "con ID cittadino di", "sv_i": "bannato per utilizzo di auto-revive", "sv_j": "bannato per apertura inventario", "sv_kicked": "<PERSON><PERSON><PERSON><PERSON>", "sv_kicked_a": "%s è stato espulso da %s per %s", "sv_kicked_b": "bannato per espulsione giocatore", "sv_ban": "ANNUNCIO", "sv_ban_a": "Giocatore <PERSON>", "sv_ban_b": "è stato bannato", "sv_ban_c": "bannato per utilizzo di teletrasporto dal giocatore", "sv_ban_d": "bannato per utilizzo di teletrasporto del giocatore", "sv_ban_e": "bannato per utilizzo di freeze del giocatore", "sv_ban_f": "bannato per utilizzo di spectate del giocatore", "sv_ban_g": "bannato per utilizzo attacco selvaggio", "sv_ban_h": "bannato per utilizzo di dai fuoco", "sv_ban_i": "bannato per utilizzo di give item", "sv_ban_j": "bannato per utilizzo di verifica info giocatore", "sv_103": "Sei stato cacciato dal server", "sv_104": "🔸 Controlla il nostro Discord per ulteriori informazioni: ", "sv_106": "Sei stato bannato:", "sv_107": "Il tuo ban è permanente.", "sv_108": "🔸 Controlla il nostro Discord per ulteriori informazioni: ", "sv_109": "Il ban scade: ", "sv_110": "🔸 Controlla il nostro Discord per ulteriori informazioni: ", "sv_111": "Blocco Giocatore Attivo", "sv_112": "hai bloccato il giocatore ", "sv_113": "Blocco Giocatore Disattivato", "sv_114": "hai sbloccato il giocatore ", "sv_135": "<PERSON><PERSON><PERSON> Con<PERSON>to", "sv_136": "oggetto inviato con successo", "sv_finan_116": "Errore", "sv_finan_117": "il giocatore non ha abbastanza ", "sv_finan_118": " da rimuovere!"}