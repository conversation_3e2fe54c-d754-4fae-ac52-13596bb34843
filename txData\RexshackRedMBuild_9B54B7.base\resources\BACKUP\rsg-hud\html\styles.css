@import url('https://fonts.googleapis.com/css2?family=Work+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');@import url('https://fonts.cdnfonts.com/css/pricedown');@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Yantramanav:wght@100;300;400;500;700;900&display=swap');@import url('https://fonts.googleapis.com/css2?family=Work+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Yantramanav:wght@100;300;400;500;700;900&display=swap');


#main-container {
    width: 100%;
    height: auto;
}

/* Money */

#money-container {
    position: absolute;
    right: 2vw;
    top: 5vh;
    font-weight: 400;
    font-size: 40px;
  }
  
  #sign {
    font-family: 'Pricedown Bl', sans-serif;
    text-align: right;
    color: #00ac31;
    text-shadow: -1px -1px 0 rgba(0,0,0, 0.7), 1px -1px 0 rgba(0,0,0, 0.7), -1px 1px 0 rgba(0,0,0, 0.7), 1px 1px 0 rgba(0,0,0, 0.7);
  }

  #signbank {
    font-family: 'Pricedown Bl', sans-serif;
    text-align: right;
    color: #D4AF37;
    text-shadow: -1px -1px 0 rgba(0,0,0, 0.7), 1px -1px 0 rgba(0,0,0, 0.7), -1px 1px 0 rgba(0,0,0, 0.7), 1px 1px 0 rgba(0,0,0, 0.7);
  }
  
  #plus {
    font-size: 50px;
    font-family: 'Pricedown Bl', sans-serif;
    text-align: right;
    color: #00ac31;
    text-shadow: -1px -1px 0 rgba(0,0,0, 0.7), 1px -1px 0 rgba(0,0,0, 0.7), -1px 1px 0 rgba(0,0,0, 0.7), 1px 1px 0 rgba(0,0,0, 0.7);
  }
  
  #minus {
    font-size: 50px;
    font-family: 'Pricedown Bl', sans-serif;
    text-align: right;
    color: #ac0000;
    text-shadow: -1px -1px 0 rgba(0,0,0, 0.7), 1px -1px 0 rgba(0,0,0, 0.7), -1px 1px 0 rgba(0,0,0, 0.7), 1px 1px 0 rgba(0,0,0, 0.7);
  }
  
  #bank {
    font-family: 'Pricedown Bl', sans-serif;
    text-align: right;
    color: #D4AF37;
    text-shadow: -1px -1px 0 rgba(0,0,0, 0.7), 1px -1px 0 rgba(0,0,0, 0.7), -1px 1px 0 rgba(0,0,0, 0.7), 1px 1px 0 rgba(0,0,0, 0.7);
  }


  #money {
    font-family: 'Pricedown Bl', sans-serif;
    text-align: right;
    color: #ffffff;
    text-shadow: -1px -1px 0 rgba(0,0,0, 0.7), 1px -1px 0 rgba(0,0,0, 0.7), -1px 1px 0 rgba(0,0,0, 0.7), 1px 1px 0 rgba(0,0,0, 0.7);
  }
  
  #bloodmoney {
    font-family: 'Pricedown Bl', sans-serif;
    text-align: right;
    color: #b02525;
    text-shadow: -1px -1px 0 rgba(0,0,0, 0.7), 1px -1px 0 rgba(0,0,0, 0.7), -1px 1px 0 rgba(0,0,0, 0.7), 1px 1px 0 rgba(0,0,0, 0.7);
  }

/* Player HUD */

#playerhud {
    position: fixed;
    left: 1vh;
    bottom: 2.5vw;
    display: internal;
}

.progress {
    margin-top: 5px!important; 
 }

/* Animation */

.slide-fade-enter-active {
    transition: all .3s ease-out;
}

.slide-fade-leave-active {
    transition: all .8s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
}
