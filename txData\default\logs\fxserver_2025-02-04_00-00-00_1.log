
================================================================
======== Log Rotated - 2/4/2025, 09:10:25                       
================================================================

================================================================
======== FXServer Starting - 2/4/2025, 09:10:36                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 79 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] [0mServer license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[38;5;171m[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] [97mStarted resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] [0mCreating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[38;5;161m[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[38;5;83m[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] [0mStarted resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-resources-core] Could not find dependency rsg-menu for resource butcher.
[ citizen-server-impl] Couldn't start resource butcher.
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] [0mCreating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] [0mCreating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] [0mCreating script environments for pma-voice
[38;5;83m[           resources] Started resource pma-voice
[38;5;83m[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-resources-core] Could not find dependency redemrp_inventory for resource redemrp_butchertable.
[ citizen-server-impl] [0mCouldn't start resource redemrp_butchertable.
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] [0mCreating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] [0mCreating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[38;5;161m[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] [0mStarted resource rsg-hud
[    c-scripting-core] [0mCreating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] [0mCreating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[38;5;161m[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[script:rsg-inventory] 1 inventories successfully loaded

================================================================
======== FXServer Starting - 2/4/2025, 09:57:27                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 79 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] [97mThe file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] [0mCreating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] [0mCreating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-resources-core] Could not find dependency rsg-menu for resource butcher.
[ citizen-server-impl] Couldn't start resource butcher.
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] [0mStarted resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] [0mCreating script environments for ox_doorlock
[38;5;83m[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-resources-core] Could not find dependency rsg_inventory for resource redemrp_butchertable.
[ citizen-server-impl] Couldn't start resource redemrp_butchertable.
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[38;5;161m[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] [0mCreating script environments for rsg-animations
[           resources] [0mStarted resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] [0mCreating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] [0mCreating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] [0mCreating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] [0mCreating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] [0mStarted resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] [0mCreating script environments for rsg-radialmenu
[38;5;83m[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0[0m
[    script:ox_target] [ox_target] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[script:rsg-inventory] 1 inventories successfully loaded
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/4/2025, 10:00:47                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 79 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] [0mStarted resource sessionmanager-rdr3
[    c-scripting-core] [0mCreating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[38;5;83m[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] [0mStarted resource rsg-core
[    c-scripting-core] [97mCreating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] [97mCreating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] [0mCreating script environments for ip-chat
[           resources] Started resource ip-chat
[38;5;161m[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-resources-core] Could not find dependency rsg-menu for resource butcher.
[ citizen-server-impl] Couldn't start resource butcher.
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] [0mStarted resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] [0mCreating script environments for redemrp_butchertable
[    c-scripting-core] [0mFailed to load script @rsg_core/shared/locale.lua.
[script:redemrp_butch] Error parsing script @redemrp_butchertable/config.lua in resource redemrp_butchertable: @redemrp_butchertable/config.lua:50: '}' expected (to close '{' at line 49) near '['
[    c-scripting-core] Failed to load script config.lua.
[script:redemrp_butch] SCRIPT ERROR: @redemrp_butchertable/server.lua:4: attempt to index a nil value (global 'RSGCore')
[script:redemrp_butch] > fn (@redemrp_butchertable/server.lua:4)
[script:redemrp_butch] Error parsing script @redemrp_butchertable/config.lua in resource redemrp_butchertable: @redemrp_butchertable/config.lua:50: '}' expected (to close '{' at line 49) near '['
[    c-scripting-core] Failed to load script config.lua.
[resources:redemrp_bu] Warning: could not find shared_script `locales/en.lua` (defined in fxmanifest.lua:19)
[           resources] Started resource redemrp_butchertable (1 warning)
[    c-scripting-core] [0mCreating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] [0mStarted resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] [0mCreating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] [0mCreating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] [0mCreating script environments for rsg-telegram
[           resources] [0mStarted resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] [0mStarted resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] [0mCreating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[38;5;83m[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] [0mStarted resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[38;5;17m[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0[0m
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.[0m
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[38;5;159m[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo][32m Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[38;5;64m[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2[0m
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 1 inventories successfully loaded
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/4/2025, 10:02:59                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 79 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] [0mServer license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [36m[txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] [97mThe file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] [0mCreating script environments for oxmysql
[           resources] [0mStarted resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-resources-core] Could not find dependency rsg-menu for resource butcher.
[ citizen-server-impl] Couldn't start resource butcher.
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] [0mStarted resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[38;5;83m[           resources] Started resource progressbar
[38;5;161m[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for redemrp_butchertable
[    c-scripting-core] [0mFailed to load script @rsg_core/shared/locale.lua.
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] [0mCreating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] [0mCreating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] [0mCreating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] [0mStarted resource rsg-lawman
[           resources] [0mStarted resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] [0mCreating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] [0mCreating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[script:rsg-multichar] [36m[rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[      script:oxmysql] [0m[11.6.2-MariaDB] Database server connection established!
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[script:rsg-inventory] 1 inventories successfully loaded
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume][32m You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [36m[rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [0m[rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[38;5;73m[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[38;5;73m[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/4/2025, 10:03:41                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 79 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[38;5;66m[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] [0mStarted resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] [0mCreating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] [97mCreating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] [97mStarted resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] [0mCreating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-resources-core] Could not find dependency rsg-menu for resource butcher.
[ citizen-server-impl] Couldn't start resource butcher.
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] [0mQUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for redemrp_butchertable
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] [0mCreating script environments for weathersync
[           resources] [0mStarted resource weathersync
[    c-scripting-core] [0mCreating script environments for xsound
[           resources] [0mStarted resource xsound
[    c-scripting-core] [0mCreating script environments for rsg-adminmenu
[           resources] [0mStarted resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] [0mStarted resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] [0mStarted resource rsg-animations
[    c-scripting-core] [0mCreating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] [0mStarted resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] [0mCreating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] [0mCreating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[38;5;120m[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo][32m Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [0m[rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[38;5;130m[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[38;5;229m[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0[0m
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[38;5;200m[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [0m[rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 1 inventories successfully loaded
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/4/2025, 10:05:16                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 78 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] [97mCreating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] [0mStarted resource ox_target
[    c-scripting-core] [0mCreating script environments for ip-chat
[           resources] Started resource ip-chat
[38;5;161m[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] [0mCreating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] [0mCreating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] [0mCreating script environments for rsg-weapons
[           resources] [0mStarted resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for redemrp_butchertable
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] Creating script environments for weathersync
[           resources] [0mStarted resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] [0mStarted resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[38;5;83m[           resources] Started resource rsg-appearance
[    c-scripting-core] [0mCreating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] [0mCreating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[38;5;161m[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] [0mCreating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 1 inventories successfully loaded
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl] [91m ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] is loading into the server
[ citizen-server-impl] server thread hitch warning: timer interval of 184 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 1058 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 157 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 196 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 517 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 203 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 701 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 489 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 343 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 342 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 374 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 808 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-multicharacter took 3727.1781ms to execute a query!
[      script:oxmysql] SELECT * FROM players WHERE license = ? ["license:10de59f3e77fd041e517a172faf4b229c64bea90"]
[ citizen-server-impl] server thread hitch warning: timer interval of 2974 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 171 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-multicharacter took 570.8025ms to execute a query!
[      script:oxmysql] SELECT * FROM playerskins WHERE citizenid = ? ["ZDL62613"]
[ citizen-server-impl] server thread hitch warning: timer interval of 204 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 489 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 151 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-core took 678.4219ms to execute a query!
[      script:oxmysql] SELECT * FROM players where citizenid = ? ["ZDL62613"]
[ citizen-server-impl] server thread hitch warning: timer interval of 982 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[script:rsg-multichar] [rsg-core] Babygang (Citizen ID: ZDL62613) has succesfully loaded!
[ citizen-server-impl] server thread hitch warning: timer interval of 2284 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 341 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-core took 1378.2413ms to execute a query!
[      script:oxmysql] INSERT INTO players (citizenid, cid, license, name, money, charinfo, job, gang, position, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE cid = ?, name = ?, money = ?, charinfo = ?, job = ?, gang = ?, position = ?, metadata = ? ["ZDL62613",1,"license:10de59f3e77fd041e517a172faf4b229c64bea90","Babygang","{\"bloodmoney\":0,\"bank\":6,\"rhobank\":0,\"cash\":99369.0,\"valbank\":0,\"blkbank\":0,\"armbank\":0}","{\"account\":\"US04RSGCore6183584342\",\"firstname\":\"SLENTENG\",\"cid\":\"1\",\"lastname\":\"SALOMO\",\"birthdate\":\"1870-01-01\",\"gender\":0,\"nationality\":\"INDONESIA\"}","{\"payment\":3,\"name\":\"unemployed\",\"isboss\":false,\"grade\":{\"level\":0,\"name\":\"Freelancer\",\"isboss\":false},\"label\":\"Civilian\",\"type\":\"none\",\"onduty\":true}","{\"grade\":{\"level\":0,\"name\":\"Unaffiliated\",\"isboss\":false},\"label\":\"No Gang\",\"name\":\"none\",\"isboss\":false}","{\"x\":-562.************,\"y\":-3776.**********,\"z\":238.*************}","{\"fingerprint\":\"vz977H61SAo4118\",\"stress\":17,\"walletid\":\"RSG-********\",\"health\":141,\"rep\":[],\"thirst\":92.4,\"armor\":0,\"cleanliness\":100.0,\"ishandcuffed\":false,\"injail\":0,\"isdead\":false,\"criminalrecord\":{\"hasRecord\":false},\"callsign\":\"NO CALLSIGN\",\"hunger\":91.6,\"bloodtype\":\"AB-\",\"status\":[],\"jailitems\":[]}",1,"Babygang","{\"bloodmoney\":0,\"bank\":6,\"rhobank\":0,\"cash\":99369.0,\"valbank\":0,\"blkbank\":0,\"armbank\":0}","{\"account\":\"US04RSGCore6183584342\",\"firstname\":\"SLENTENG\",\"cid\":\"1\",\"lastname\":\"SALOMO\",\"birthdate\":\"1870-01-01\",\"gender\":0,\"nationality\":\"INDONESIA\"}","{\"payment\":3,\"name\":\"unemployed\",\"isboss\":false,\"grade\":{\"level\":0,\"name\":\"Freelancer\",\"isboss\":false},\"label\":\"Civilian\",\"type\":\"none\",\"onduty\":true}","{\"grade\":{\"level\":0,\"name\":\"Unaffiliated\",\"isboss\":false},\"label\":\"No Gang\",\"name\":\"none\",\"isboss\":false}","{\"x\":-562.************,\"y\":-3776.**********,\"z\":238.*************}","{\"fingerprint\":\"vz977H61SAo4118\",\"stress\":17,\"walletid\":\"RSG-********\",\"health\":141,\"rep\":[],\"thirst\":92.4,\"armor\":0,\"cleanliness\":100.0,\"ishandcuffed\":false,\"injail\":0,\"isdead\":false,\"criminalrecord\":{\"hasRecord\":false},\"callsign\":\"NO CALLSIGN\",\"hunger\":91.6,\"bloodtype\":\"AB-\",\"status\":[],\"jailitems\":[]}"]
[ citizen-server-impl] server thread hitch warning: timer interval of 613 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-inventory took 1904.4414ms to execute a query!
[      script:oxmysql] UPDATE players SET inventory = ? WHERE citizenid = ? ["[{\"name\":\"water\",\"info\":[],\"type\":\"item\",\"amount\":12,\"slot\":1},{\"name\":\"bread\",\"info\":[],\"type\":\"item\",\"amount\":13,\"slot\":2},{\"name\":\"weapon_revolver_cattleman\",\"info\":{\"serie\":\"75pTe5oA775gcvp\",\"quality\":94.8},\"type\":\"weapon\",\"amount\":1,\"slot\":3},{\"name\":\"ammo_box_revolver\",\"info\":[],\"type\":\"item\",\"amount\":14,\"slot\":4},{\"name\":\"weapon_repeater_evans\",\"info\":{\"serie\":\"46ybg5fE673OVJh\",\"quality\":98.8},\"type\":\"weapon\",\"amount\":1,\"slot\":5},{\"name\":\"ammo_box_repeater\",\"info\":[],\"type\":\"item\",\"amount\":9,\"slot\":6}]","ZDL62613"]
[ citizen-server-impl] server thread hitch warning: timer interval of 403 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 1104 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 1251 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[      script:oxmysql] [11.6.2-MariaDB] rsg-core took 301.1121ms to execute a query!
[      script:oxmysql] INSERT INTO players (citizenid, cid, license, name, money, charinfo, job, gang, position, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE cid = ?, name = ?, money = ?, charinfo = ?, job = ?, gang = ?, position = ?, metadata = ? ["ZDL62613",1,"license:10de59f3e77fd041e517a172faf4b229c64bea90","Babygang","{\"bloodmoney\":0,\"bank\":6,\"rhobank\":0,\"cash\":99369.0,\"valbank\":0,\"blkbank\":0,\"armbank\":0}","{\"account\":\"US04RSGCore6183584342\",\"firstname\":\"SLENTENG\",\"cid\":\"1\",\"lastname\":\"SALOMO\",\"birthdate\":\"1870-01-01\",\"gender\":0,\"nationality\":\"INDONESIA\"}","{\"payment\":3,\"name\":\"unemployed\",\"isboss\":false,\"grade\":{\"level\":0,\"name\":\"Freelancer\",\"isboss\":false},\"label\":\"Civilian\",\"type\":\"none\",\"onduty\":true}","{\"grade\":{\"level\":0,\"name\":\"Unaffiliated\",\"isboss\":false},\"label\":\"No Gang\",\"name\":\"none\",\"isboss\":false}","{\"x\":-1303.*************,\"y\":394.*************,\"z\":95.*************}","{\"fingerprint\":\"vz977H61SAo4118\",\"stress\":17,\"walletid\":\"RSG-********\",\"health\":141,\"rep\":[],\"thirst\":88.**************,\"armor\":0,\"cleanliness\":100.0,\"ishandcuffed\":false,\"injail\":0,\"isdead\":false,\"criminalrecord\":{\"hasRecord\":false},\"callsign\":\"NO CALLSIGN\",\"hunger\":87.**************,\"bloodtype\":\"AB-\",\"status\":[],\"jailitems\":[]}",1,"Babygang","{\"bloodmoney\":0,\"bank\":6,\"rhobank\":0,\"cash\":99369.0,\"valbank\":0,\"blkbank\":0,\"armbank\":0}","{\"account\":\"US04RSGCore6183584342\",\"firstname\":\"SLENTENG\",\"cid\":\"1\",\"lastname\":\"SALOMO\",\"birthdate\":\"1870-01-01\",\"gender\":0,\"nationality\":\"INDONESIA\"}","{\"payment\":3,\"name\":\"unemployed\",\"isboss\":false,\"grade\":{\"level\":0,\"name\":\"Freelancer\",\"isboss\":false},\"label\":\"Civilian\",\"type\":\"none\",\"onduty\":true}","{\"grade\":{\"level\":0,\"name\":\"Unaffiliated\",\"isboss\":false},\"label\":\"No Gang\",\"name\":\"none\",\"isboss\":false}","{\"x\":-1303.*************,\"y\":394.*************,\"z\":95.*************}","{\"fingerprint\":\"vz977H61SAo4118\",\"stress\":17,\"walletid\":\"RSG-********\",\"health\":141,\"rep\":[],\"thirst\":88.**************,\"armor\":0,\"cleanliness\":100.0,\"ishandcuffed\":false,\"injail\":0,\"isdead\":false,\"criminalrecord\":{\"hasRecord\":false},\"callsign\":\"NO CALLSIGN\",\"hunger\":87.**************,\"bloodtype\":\"AB-\",\"status\":[],\"jailitems\":[]}"]
[ citizen-server-impl] server thread hitch warning: timer interval of 247 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-inventory took 301.2857ms to execute a query!
[      script:oxmysql] UPDATE players SET inventory = ? WHERE citizenid = ? ["[{\"name\":\"water\",\"info\":[],\"type\":\"item\",\"amount\":12,\"slot\":1},{\"name\":\"bread\",\"info\":[],\"type\":\"item\",\"amount\":13,\"slot\":2},{\"name\":\"weapon_revolver_cattleman\",\"info\":{\"serie\":\"75pTe5oA775gcvp\",\"quality\":94.8},\"type\":\"weapon\",\"amount\":1,\"slot\":3},{\"name\":\"ammo_box_revolver\",\"info\":[],\"type\":\"item\",\"amount\":14,\"slot\":4},{\"name\":\"weapon_repeater_evans\",\"info\":{\"serie\":\"46ybg5fE673OVJh\",\"quality\":98.8},\"type\":\"weapon\",\"amount\":1,\"slot\":5},{\"name\":\"ammo_box_repeater\",\"info\":[],\"type\":\"item\",\"amount\":9,\"slot\":6}]","ZDL62613"]
[      script:oxmysql] [11.6.2-MariaDB] rsg-ammo took 547.1869ms to execute a query!
[      script:oxmysql] SELECT * FROM player_ammo WHERE citizenid = ? LIMIT 1 ["ZDL62613"]
[ citizen-server-impl] sync thread hitch warning: timer interval of 452 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 183 milliseconds
[38;5;73m[ citizen-server-impl] server thread hitch warning: timer interval of 174 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 155 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 310 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 310 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 1557 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 563 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 450 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 555 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-ammo took 803.0049ms to execute a query!
[      script:oxmysql] UPDATE player_ammo SET ammo_revolver_express = ?, ammo_revolver_express_explosive = ?, ammo_revolver_split_point = ?, ammo_rifle_high_velocity = ?, ammo_hatchet_cleaver = ?, ammo_repeater_express = ?, ammo_revolver_high_velocity = ?, ammo_repeater_express_explosive = ?, ammo_arrow_poison = ?, ammo_throwing_knives_poison = ?, ammo_pistol_express = ?, ammo_revolver = ?, ammo_rifle_express = ?, ammo_bolas_hawkmoth = ?, ammo_molotov = ?, ammo_repeater = ?, ammo_pistol_high_velocity = ?, ammo_arrow_fire = ?, ammo_bolas = ?, ammo_22_tranquilizer = ?, ammo_throwing_knives_drain = ?, ammo_dynamite = ?, ammo_22 = ?, ammo_rifle_elephant = ?, ammo_arrow = ?, ammo_tomahawk_ancient = ?, ammo_throwing_knives = ?, ammo_arrow_small_game = ?, ammo_tomahawk = ?, ammo_shotgun_slug_explosive = ?, ammo_shotgun = ?, ammo_shotgun_slug = ?, ammo_pistol_split_point = ?, ammo_repeater_high_velocity = ?, ammo_bolas_intertwined = ?, ammo_repeater_split_point = ?, ammo_hatchet_hunter = ?, ammo_poisonbottle = ?, ammo_shotgun_buckshot_incendiary = ?, ammo_rifle_express_explosive = ?, ammo_hatchet = ?, ammo_pistol_express_explosive = ?, ammo_arrow_dynamite = ?, ammo_pistol = ?, ammo_rifle_split_point = ?, ammo_bolas_ironspiked = ?, ammo_rifle = ? WHERE citizenid = ? [0,0,0,0,0,0,0,0,0,0,0,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,"ZDL62613"]
[ citizen-server-impl] server thread hitch warning: timer interval of 251 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 1195 milliseconds
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] is loading into the server
[ citizen-server-impl] server thread hitch warning: timer interval of 186 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 267 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[script:rsg-multichar] [rsg-core] Babygang (Citizen ID: ZDL62613) has succesfully loaded!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 1659 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 408 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 569 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 617 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 157 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 605 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 156 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 1031 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 260 milliseconds
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[ script:connectqueue] [93mQUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] is loading into the server
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[script:rsg-multichar] [rsg-core] Babygang (Citizen ID: ZDL62613) has succesfully loaded!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 234 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] sync thread hitch warning: timer interval of 265 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 307 milliseconds
[ citizen-server-impl] network thread hitch warning: timer interval of 482 milliseconds
[      script:monitor] [txAdmin] HeartBeat failed with code 0 and message: nil
[ citizen-server-impl] server thread hitch warning: timer interval of 611 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] [97mserver thread hitch warning: timer interval of 162 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 300 milliseconds
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/4/2025, 10:35:18                 
================================================================
[38;5;171m[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 78 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[38;5;161m[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[38;5;83m[           resources] [97mStarted resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] [0mStarted resource rsg-core
[    c-scripting-core] [0mCreating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] [0mCreating script environments for connectqueue
[ script:connectqueue] [97mQUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] [0mCreating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for redemrp_butchertable
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] [0mStarted resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[38;5;83m[           resources] Started resource rsg-adminmenu
[    c-scripting-core] [0mCreating script environments for rsg-ammo
[           resources] [0mStarted resource rsg-ammo
[    c-scripting-core] [0mCreating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] [0mCreating script environments for rsg-appearance
[           resources] [0mStarted resource rsg-appearance
[    c-scripting-core] [0mCreating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[38;5;161m[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] [0mCreating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] [0mStarted resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[ citizen-server-impl] [0mserver thread hitch warning: timer interval of 256 milliseconds
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 1 inventories successfully loaded
[ citizen-server-impl] [97m        fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[ script:connectqueue] [93mQUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] is loading into the server
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[38;5;113m[script:rsg-multichar] [rsg-core] Babygang (Citizen ID: ZDL62613) has succesfully loaded!
[     script:rsg-core] [32m[rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG][0m Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!

================================================================
======== FXServer Starting - 2/4/2025, 14:01:21                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 80 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] Creating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[    c-scripting-core] Creating script environments for jp-mining
[38;5;83m[           resources] Started resource jp-mining
[           resources] Started resource menu_base
[    c-scripting-core] [0mCreating script environments for moonshine_job
[           resources] [0mStarted resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] [0mCreating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for redemrp_butchertable
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] [0mCreating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[38;5;83m[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] [0mCreating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] [0mCreating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-hanging
[  script:rsg-hanging] SCRIPT ERROR: @rsg-hanging/server/sv_main.lua:69: attempt to call a nil value (global 'CheckVersion')
[           resources] Started resource rsg-hanging
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] [0mCreating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] Creating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] [0mCreating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] [0mCreating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] [0mCreating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] [0mCreating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 1 inventories successfully loaded
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[      script:monitor] [txAdmin] Server shutdown imminent. Kicking all players.

================================================================
======== FXServer Starting - 2/4/2025, 14:06:07                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 80 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] [97mStarted resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] Creating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] [0mCreating script environments for rsg-core
[           resources] Started resource rsg-core
[    c-scripting-core] [97mCreating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] Started resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] [97mStarted resource PolyZone
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] [0mQUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[    c-scripting-core] Creating script environments for jp-mining
[           resources] Started resource jp-mining
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] Creating script environments for pma-voice
[           resources] Started resource pma-voice
[           resources] Started resource progressbar
[    c-scripting-core] [0mCreating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for redemrp_butchertable
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] Creating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] [0mCreating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] [0mCreating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] [0mCreating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] Creating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] [0mCreating script environments for rsg-horses
[           resources] [0mStarted resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] Started resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] [0mCreating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] [0mCreating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] Creating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] Creating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.
[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[38;5;230m[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 1 inventories successfully loaded
[ citizen-server-impl]         fff                          
[38;5;73m[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl] [91m ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: [0mhttps://onad-cb2efd-97k36y.users.cfx.re/
[ citizen-server-impl] server thread hitch warning: timer interval of 237 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 167 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 649 milliseconds
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[      script:oxmysql] [0m[11.6.2-MariaDB] rsg-core took 366.5956ms to execute a query!
[      script:oxmysql] SELECT id, reason, expire FROM bans WHERE license = ? ["license:10de59f3e77fd041e517a172faf4b229c64bea90"]
[ script:connectqueue] [97mQUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] is loading into the server
[ citizen-server-impl] server thread hitch warning: timer interval of 1167 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 114 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 176 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 193 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 231 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 152 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[script:rsg-multichar] [rsg-core] Babygang (Citizen ID: ZDL62613) has succesfully loaded!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] sync thread hitch warning: timer interval of 757 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-ammo took 370.0307ms to execute a query!
[      script:oxmysql] UPDATE player_ammo SET ammo_dynamite = ?, ammo_rifle_express_explosive = ?, ammo_repeater_express_explosive = ?, ammo_arrow_small_game = ?, ammo_throwing_knives_drain = ?, ammo_revolver_express = ?, ammo_hatchet = ?, ammo_rifle_split_point = ?, ammo_repeater_express = ?, ammo_tomahawk = ?, ammo_pistol = ?, ammo_pistol_express = ?, ammo_throwing_knives_poison = ?, ammo_rifle_high_velocity = ?, ammo_22 = ?, ammo_pistol_split_point = ?, ammo_molotov = ?, ammo_tomahawk_ancient = ?, ammo_arrow = ?, ammo_throwing_knives = ?, ammo_bolas_ironspiked = ?, ammo_shotgun_slug = ?, ammo_arrow_poison = ?, ammo_bolas_intertwined = ?, ammo_arrow_dynamite = ?, ammo_pistol_express_explosive = ?, ammo_hatchet_cleaver = ?, ammo_22_tranquilizer = ?, ammo_poisonbottle = ?, ammo_bolas_hawkmoth = ?, ammo_pistol_high_velocity = ?, ammo_shotgun = ?, ammo_rifle_express = ?, ammo_shotgun_slug_explosive = ?, ammo_revolver_high_velocity = ?, ammo_revolver = ?, ammo_repeater_split_point = ?, ammo_shotgun_buckshot_incendiary = ?, ammo_repeater_high_velocity = ?, ammo_arrow_fire = ?, ammo_hatchet_hunter = ?, ammo_revolver_split_point = ?, ammo_rifle_elephant = ?, ammo_revolver_express_explosive = ?, ammo_bolas = ?, ammo_repeater = ?, ammo_rifle = ? WHERE citizenid = ? [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,19,0,0,0,0,0,0,0,0,0,0,0,"ZDL62613"]
[ citizen-server-impl] server thread hitch warning: timer interval of 367 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-ammo took 383.4861ms to execute a query!
[      script:oxmysql] UPDATE player_ammo SET ammo_dynamite = ?, ammo_rifle_express_explosive = ?, ammo_repeater_express_explosive = ?, ammo_arrow_small_game = ?, ammo_throwing_knives_drain = ?, ammo_revolver_express = ?, ammo_hatchet = ?, ammo_rifle_split_point = ?, ammo_repeater_express = ?, ammo_tomahawk = ?, ammo_pistol = ?, ammo_pistol_express = ?, ammo_throwing_knives_poison = ?, ammo_rifle_high_velocity = ?, ammo_22 = ?, ammo_pistol_split_point = ?, ammo_molotov = ?, ammo_tomahawk_ancient = ?, ammo_arrow = ?, ammo_throwing_knives = ?, ammo_bolas_ironspiked = ?, ammo_shotgun_slug = ?, ammo_arrow_poison = ?, ammo_bolas_intertwined = ?, ammo_arrow_dynamite = ?, ammo_pistol_express_explosive = ?, ammo_hatchet_cleaver = ?, ammo_22_tranquilizer = ?, ammo_poisonbottle = ?, ammo_bolas_hawkmoth = ?, ammo_pistol_high_velocity = ?, ammo_shotgun = ?, ammo_rifle_express = ?, ammo_shotgun_slug_explosive = ?, ammo_revolver_high_velocity = ?, ammo_revolver = ?, ammo_repeater_split_point = ?, ammo_shotgun_buckshot_incendiary = ?, ammo_repeater_high_velocity = ?, ammo_arrow_fire = ?, ammo_hatchet_hunter = ?, ammo_revolver_split_point = ?, ammo_rifle_elephant = ?, ammo_revolver_express_explosive = ?, ammo_bolas = ?, ammo_repeater = ?, ammo_rifle = ? WHERE citizenid = ? [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,19,0,0,0,0,0,0,0,0,0,0,0,"ZDL62613"]
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG][0m Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[38;5;73m[ citizen-server-impl] server thread hitch warning: timer interval of 250 milliseconds
> txaReportResources
[      script:monitor] [txAdmin] Sending resources list to txAdmin.
[ citizen-server-impl] server thread hitch warning: timer interval of 226 milliseconds
> ensure "rsg-inventory"
[           resources] Stopping resource redemrp_butchertable
> txaReportResources
[           resources] Stopping resource rsg-shops
[           resources] Stopping resource rsg-inventory
> txaReportResources
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂ONAD-cb2efd＂,＂message＂:＂Server restarting (requested by ONAD-cb2efd).＂}"
[    c-scripting-core] Creating script environments for rsg-inventory

================================================================
======== FXServer Starting - 2/4/2025, 14:34:43                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 80 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
> txaEvent "serverShuttingDown" "{＂delay＂:5000,＂author＂:＂txAdmin＂,＂message＂:＂Server restarting (failed to start in time).＂}"
[    c-scripting-core] Creating script environments for yarn

================================================================
======== FXServer Starting - 2/4/2025, 14:35:48                 
================================================================
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[           resources] Scanning resources.
[    resources:BACKUP] Warning: BACKUP does not have a resource manifest (fxmanifest.lua)
[           resources] Found 80 resources.
[           resources] 1 warning was encountered.
[          svadhesive] Authenticating server license key...
[          svadhesive] Server license key authentication succeeded. Welcome!
[    c-scripting-core] Creating script environments for yarn
[           resources] Started resource yarn
[    c-scripting-core] Creating script environments for sessionmanager-rdr3
[           resources] Started resource sessionmanager-rdr3
[    c-scripting-core] Creating script environments for monitor
[      script:monitor] [txAdmin] Resource v6.0.2 threads and commands set up. All Ready.
[           resources] Started resource monitor
[ citizen-server-impl] The file myLogo.png is not a PNG file.
[                 cmd] Argument count mismatch (passed 3, wanted 2)
[    c-scripting-core] [97mCreating script environments for mapmanager
[           resources] Started resource mapmanager
[           resources] Started resource spawnmanager
[    c-scripting-core] Creating script environments for oxmysql
[           resources] Started resource oxmysql
[    c-scripting-core] Creating script environments for ox_lib
[           resources] Started resource ox_lib
[    c-scripting-core] Creating script environments for rsg-core
[           resources] [97mStarted resource rsg-core
[    c-scripting-core] [97mCreating script environments for rsg-multicharacter
[           resources] Started resource rsg-multicharacter
[    c-scripting-core] Creating script environments for rsg-spawn
[           resources] Started resource rsg-spawn
[    c-scripting-core] Creating script environments for rsg-menubase
[           resources] Started resource rsg-menubase
[    c-scripting-core] Creating script environments for ox_target
[           resources] [97mStarted resource ox_target
[    c-scripting-core] Creating script environments for ip-chat
[           resources] Started resource ip-chat
[    c-scripting-core] Creating script environments for PolyZone
[           resources] Started resource PolyZone
[    c-scripting-core] Creating script environments for connectqueue
[ script:connectqueue] QUEUE:  [connectqueue] Disabling hardcap 
[           resources] Started resource connectqueue
[           resources] Started resource db-femped
[    c-scripting-core] Creating script environments for interact-sound
[           resources] Started resource interact-sound
[    c-scripting-core] Creating script environments for jp-mining
[           resources] Started resource jp-mining
[           resources] Started resource menu_base
[    c-scripting-core] Creating script environments for moonshine_job
[           resources] Started resource moonshine_job
[    c-scripting-core] Creating script environments for ox_doorlock
[           resources] Started resource ox_doorlock
[    c-scripting-core] [0mCreating script environments for pma-voice
[           resources] Started resource pma-voice
[38;5;83m[           resources] Started resource progressbar
[    c-scripting-core] Creating script environments for rNotify
[           resources] Started resource rNotify
[    c-scripting-core] Creating script environments for redemrp_Deliveryjob
[           resources] Started resource redemrp_Deliveryjob
[    c-scripting-core] Creating script environments for rsg-weapons
[           resources] Started resource rsg-weapons
[    c-scripting-core] Creating script environments for rsg-inventory
[           resources] Started resource rsg-inventory
[    c-scripting-core] Creating script environments for redemrp_butchertable
[           resources] Started resource redemrp_butchertable
[    c-scripting-core] Creating script environments for weathersync
[           resources] Started resource weathersync
[    c-scripting-core] [0mCreating script environments for xsound
[           resources] Started resource xsound
[    c-scripting-core] Creating script environments for rsg-adminmenu
[           resources] Started resource rsg-adminmenu
[    c-scripting-core] Creating script environments for rsg-ammo
[           resources] Started resource rsg-ammo
[    c-scripting-core] Creating script environments for rsg-animations
[           resources] Started resource rsg-animations
[    c-scripting-core] Creating script environments for rsg-appearance
[           resources] Started resource rsg-appearance
[    c-scripting-core] Creating script environments for rsg-banking
[           resources] Started resource rsg-banking
[    c-scripting-core] Creating script environments for rsg-barbers
[           resources] Started resource rsg-barbers
[    c-scripting-core] Creating script environments for rsg-wardrobe
[           resources] Started resource rsg-wardrobe
[    c-scripting-core] Creating script environments for rsg-bathing
[           resources] Started resource rsg-bathing
[    c-scripting-core] Creating script environments for rsg-bossmenu
[           resources] Started resource rsg-bossmenu
[    c-scripting-core] Creating script environments for rsg-canteen
[           resources] Started resource rsg-canteen
[    c-scripting-core] Creating script environments for rsg-consume
[           resources] Started resource rsg-consume
[    c-scripting-core] [0mCreating script environments for rsg-doorlock
[           resources] Started resource rsg-doorlock
[    c-scripting-core] Creating script environments for rsg-essentials
[           resources] Started resource rsg-essentials
[    c-scripting-core] Creating script environments for rsg-fishing
[           resources] Started resource rsg-fishing
[    c-scripting-core] Creating script environments for rsg-gangmenu
[           resources] Started resource rsg-gangmenu
[    c-scripting-core] Creating script environments for rsg-hanging
[           resources] Started resource rsg-hanging
[    c-scripting-core] Creating script environments for rsg-horses
[           resources] Started resource rsg-horses
[    c-scripting-core] Creating script environments for rsg-telegram
[           resources] [0mStarted resource rsg-telegram
[    c-scripting-core] Creating script environments for rsg-hud
[           resources] Started resource rsg-hud
[    c-scripting-core] Creating script environments for rsg-lawman
[           resources] Started resource rsg-lawman
[           resources] Started resource rsg-lockpick
[    c-scripting-core] Creating script environments for rsg-medic
[           resources] Started resource rsg-medic
[    c-scripting-core] [0mCreating script environments for rsg-multijob
[           resources] Started resource rsg-multijob
[    c-scripting-core] Creating script environments for rsg-npcs
[           resources] Started resource rsg-npcs
[    c-scripting-core] Creating script environments for rsg-playerinfo
[           resources] Started resource rsg-playerinfo
[    c-scripting-core] Creating script environments for rsg-prison
[           resources] Started resource rsg-prison
[    c-scripting-core] [0mCreating script environments for rsg-radialmenu
[           resources] Started resource rsg-radialmenu
[    c-scripting-core] Creating script environments for rsg-shops
[           resources] Started resource rsg-shops
[    c-scripting-core] [0mCreating script environments for redm-ipls
[           resources] Started resource redm-ipls
[           resources] Started resource redm-ymaps
[38;5;113m[script:rsg-multichar] [rsg-multicharacter] Current Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] Latest Version: 2.3.0
[script:rsg-multichar] [rsg-multicharacter] You are running the latest version.
[     script:rsg-core] [rsg-core] Current Version: 2.1.4
[     script:rsg-core] [rsg-core] Latest Version: 2.1.4
[     script:rsg-core] [rsg-core] You are running the latest version.[0m
[    script:rsg-spawn] [rsg-spawn] Current Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] Latest Version: 2.0.2
[    script:rsg-spawn] [rsg-spawn] You are running the latest version.
[ script:rsg-menubase] [rsg-menubase] Current Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] Latest Version: 1.1.3
[ script:rsg-menubase] [rsg-menubase] You are running the latest version.
[    script:ox_target] [ox_target] Current Version: 2.0.0
[    script:ox_target] [ox_target] Latest Version: 2.0.0
[    script:ox_target] [ox_target] You are running the latest version.
[script:interact-soun] [interact-sound] Current Version: 1.0.2
[script:interact-soun] [interact-sound] Latest Version: 1.0.2
[script:interact-soun] [interact-sound] You are running the latest version.
[  script:ox_doorlock] [ox_doorlock] Current Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] Latest Version: 2.0.0
[  script:ox_doorlock] [ox_doorlock] You are running the latest version.
[  script:rsg-weapons] [rsg-weapons] Current Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] Latest Version: 2.1.0
[  script:rsg-weapons] [rsg-weapons] You are running the latest version.
[script:rsg-inventory] [rsg-inventory] Current Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] Latest Version: 2.1.9
[script:rsg-inventory] [rsg-inventory] You are running the latest version.
[script:rsg-adminmenu] [rsg-adminmenu] Current Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] Latest Version: 2.0.0
[script:rsg-adminmenu] [rsg-adminmenu] You are running the latest version.
[     script:rsg-ammo] [rsg-ammo] Current Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] Latest Version: 2.1.0
[     script:rsg-ammo] [rsg-ammo] You are running the latest version.
[script:rsg-animation] [rsg-animations] Current Version: 2.0.0
[script:rsg-animation] [rsg-animations] Latest Version: 2.0.0
[script:rsg-animation] [rsg-animations] You are running the latest version.
[script:rsg-appearanc] [rsg-appearance] Current Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] Latest Version: 2.4.0
[script:rsg-appearanc] [rsg-appearance] You are running the latest version.
[  script:rsg-banking] [rsg-banking] Current Version: 2.0.2
[  script:rsg-banking] [rsg-banking] Latest Version: 2.0.2
[  script:rsg-banking] [rsg-banking] You are running the latest version.
[  script:rsg-barbers] [rsg-barbers] Current Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] Latest Version: 2.0.2
[  script:rsg-barbers] [rsg-barbers] You are running the latest version.
[ script:rsg-wardrobe] [rsg-wardrobe] Current Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] Latest Version: 2.0.0
[ script:rsg-wardrobe] [rsg-wardrobe] You are running the latest version.
[  script:rsg-bathing] [rsg-bathing] Current Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] Latest Version: 1.1.0
[  script:rsg-bathing] [rsg-bathing] You are running the latest version.
[ script:rsg-bossmenu] [rsg-bossmenu] Current Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] Latest Version: 2.0.0
[ script:rsg-bossmenu] [rsg-bossmenu] You are running the latest version.
[  script:rsg-canteen] [rsg-canteen] Current Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] Latest Version: 2.0.1
[  script:rsg-canteen] [rsg-canteen] You are running the latest version.
[  script:rsg-consume] [rsg-consume] Current Version: 1.0.5
[  script:rsg-consume] [rsg-consume] Latest Version: 1.0.5
[  script:rsg-consume] [rsg-consume] You are running the latest version.
[ script:rsg-doorlock] [rsg-doorlock] Current Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] Latest Version: 1.0.3
[ script:rsg-doorlock] [rsg-doorlock] You are running the latest version.
[script:rsg-essential] [rsg-essentials] Current Version: 2.3.1
[script:rsg-essential] [rsg-essentials] Latest Version: 2.3.1
[script:rsg-essential] [rsg-essentials] You are running the latest version.
[  script:rsg-fishing] [rsg-fishing] Current Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] Latest Version: 2.0.2
[  script:rsg-fishing] [rsg-fishing] You are running the latest version.
[ script:rsg-gangmenu] [rsg-gangmenu] Current Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] Latest Version: 2.0.0
[ script:rsg-gangmenu] [rsg-gangmenu] You are running the latest version.
[   script:rsg-horses] [rsg-horses] Current Version: 2.0.5
[   script:rsg-horses] [rsg-horses] Latest Version: 2.0.5
[   script:rsg-horses] [rsg-horses] You are running the latest version.
[ script:rsg-telegram] [rsg-telegram] Current Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] Latest Version: 2.0.4
[ script:rsg-telegram] [rsg-telegram] You are running the latest version.
[      script:rsg-hud] [rsg-hud] Current Version: 2.0.0
[      script:rsg-hud] [rsg-hud] Latest Version: 2.0.0
[      script:rsg-hud] [rsg-hud] You are running the latest version.
[   script:rsg-lawman] [rsg-lawman] Current Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] Latest Version: 2.0.0
[   script:rsg-lawman] [rsg-lawman] You are running the latest version.
[    script:rsg-medic] [rsg-medic] Current Version: 2.0.4
[    script:rsg-medic] [rsg-medic] Latest Version: 2.0.4
[    script:rsg-medic] [rsg-medic] You are running the latest version.
[ script:rsg-multijob] [rsg-multijob] Current Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] Latest Version: 2.0.1
[ script:rsg-multijob] [rsg-multijob] You are running the latest version.
[     script:rsg-npcs] [rsg-npcs] You are running the latest version.
[script:rsg-playerinf] [rsg-playerinfo] Current Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] Latest Version: 2.0.0
[script:rsg-playerinf] [rsg-playerinfo] You are running the latest version.
[ citizen-server-impl] server thread hitch warning: timer interval of 240 milliseconds
[   script:rsg-prison] [rsg-prison] Current Version: 2.0.1
[   script:rsg-prison] [rsg-prison] Latest Version: 2.0.1
[   script:rsg-prison] [rsg-prison] You are running the latest version.
[script:rsg-radialmen] [rsg-radialmenu] Current Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] Latest Version: 1.1.2
[script:rsg-radialmen] [rsg-radialmenu] You are running the latest version.
[    script:rsg-shops] [0m[rsg-shops] Current Version: 2.0.7
[    script:rsg-shops] [rsg-shops] Latest Version: 2.0.7
[    script:rsg-shops] [rsg-shops] You are running the latest version.
[    script:redm-ipls] [redm-ipls] Current Version: 1.0.0
[    script:redm-ipls] [redm-ipls] Latest Version: 1.0.0
[    script:redm-ipls] [redm-ipls] You are running the latest version.
[      script:oxmysql] [11.6.2-MariaDB] Database server connection established!
[script:rsg-inventory] 1 inventories successfully loaded
[ citizen-server-impl]         fff                          
[ citizen-server-impl]   cccc ff   xx  xx     rr rr    eee  
[ citizen-server-impl] cc     ffff   xx       rrr  r ee   e 
[ citizen-server-impl] cc     ff     xx   ... rr     eeeee  
[ citizen-server-impl]  ccccc ff   xx  xx ... rr      eeeee 
[ citizen-server-impl]                                      
[ citizen-server-impl] Authenticated with cfx.re Nucleus: https://onad-cb2efd-97k36y.users.cfx.re/
[ citizen-server-impl] [0mserver thread hitch warning: timer interval of 1250 milliseconds
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] was placed 1/1 in queue
[ citizen-server-impl] server thread hitch warning: timer interval of 563 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 753 milliseconds
[ script:connectqueue] QUEUE: Babygang[license:10de59f3e77fd041e517a172faf4b229c64bea90] is loading into the server
[      script:oxmysql] [11.6.2-MariaDB] rsg-core took 610.1449ms to execute a query!
[      script:oxmysql] SELECT id, reason, expire FROM bans WHERE license = ? ["license:10de59f3e77fd041e517a172faf4b229c64bea90"]
[ citizen-server-impl] server thread hitch warning: timer interval of 300 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 175 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 111 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 303 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 317 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 153 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 134 milliseconds
[ citizen-server-impl] sync thread hitch warning: timer interval of 115 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 179 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-multicharacter took 875.1082ms to execute a query!
[      script:oxmysql] SELECT * FROM playerskins WHERE citizenid = ? ["ZDL62613"]
[ citizen-server-impl] server thread hitch warning: timer interval of 821 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 348 milliseconds
[     script:rsg-core] [0m[rsg-core:LOG] Babygang PLAYER SAVED!
[script:rsg-multichar] [rsg-core] Babygang (Citizen ID: ZDL62613) has succesfully loaded!
[ citizen-server-impl] server thread hitch warning: timer interval of 219 milliseconds
[ citizen-server-impl] server thread hitch warning: timer interval of 486 milliseconds
[      script:oxmysql] [11.6.2-MariaDB] rsg-hud took 485.3125ms to execute a query!
[      script:oxmysql] SELECT outlawstatus FROM players WHERE citizenid = ? ["ZDL62613"]
[ citizen-server-impl] sync thread hitch warning: timer interval of 109 milliseconds
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] sync thread hitch warning: timer interval of 783 milliseconds
[     script:rsg-core] [32m[rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[     script:rsg-core] [rsg-core:LOG] Babygang PLAYER SAVED!
[ citizen-server-impl] server thread hitch warning: timer interval of 253 milliseconds
