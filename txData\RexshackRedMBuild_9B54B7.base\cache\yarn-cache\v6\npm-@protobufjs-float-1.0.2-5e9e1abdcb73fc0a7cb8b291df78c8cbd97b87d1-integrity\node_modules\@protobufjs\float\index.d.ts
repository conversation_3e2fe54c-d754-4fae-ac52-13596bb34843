/**
 * Writes a 32 bit float to a buffer using little endian byte order.
 * @name writeFloatLE
 * @function
 * @param {number} val Value to write
 * @param {Uint8Array} buf Target buffer
 * @param {number} pos Target buffer offset
 * @returns {undefined}
 */
export function writeFloatLE(val: number, buf: Uint8Array, pos: number): void;

/**
 * Writes a 32 bit float to a buffer using big endian byte order.
 * @name writeFloatBE
 * @function
 * @param {number} val Value to write
 * @param {Uint8Array} buf Target buffer
 * @param {number} pos Target buffer offset
 * @returns {undefined}
 */
export function writeFloatBE(val: number, buf: Uint8Array, pos: number): void;

/**
 * Reads a 32 bit float from a buffer using little endian byte order.
 * @name readFloatLE
 * @function
 * @param {Uint8Array} buf Source buffer
 * @param {number} pos Source buffer offset
 * @returns {number} Value read
 */
export function readFloatLE(buf: Uint8Array, pos: number): number;

/**
 * Reads a 32 bit float from a buffer using big endian byte order.
 * @name readFloatBE
 * @function
 * @param {Uint8Array} buf Source buffer
 * @param {number} pos Source buffer offset
 * @returns {number} Value read
 */
export function readFloatBE(buf: Uint8Array, pos: number): number;

/**
 * Writes a 64 bit double to a buffer using little endian byte order.
 * @name writeDoubleLE
 * @function
 * @param {number} val Value to write
 * @param {Uint8Array} buf Target buffer
 * @param {number} pos Target buffer offset
 * @returns {undefined}
 */
export function writeDoubleLE(val: number, buf: Uint8Array, pos: number): void;

/**
 * Writes a 64 bit double to a buffer using big endian byte order.
 * @name writeDoubleBE
 * @function
 * @param {number} val Value to write
 * @param {Uint8Array} buf Target buffer
 * @param {number} pos Target buffer offset
 * @returns {undefined}
 */
export function writeDoubleBE(val: number, buf: Uint8Array, pos: number): void;

/**
 * Reads a 64 bit double from a buffer using little endian byte order.
 * @name readDoubleLE
 * @function
 * @param {Uint8Array} buf Source buffer
 * @param {number} pos Source buffer offset
 * @returns {number} Value read
 */
export function readDoubleLE(buf: Uint8Array, pos: number): number;

/**
 * Reads a 64 bit double from a buffer using big endian byte order.
 * @name readDoubleBE
 * @function
 * @param {Uint8Array} buf Source buffer
 * @param {number} pos Source buffer offset
 * @returns {number} Value read
 */
export function readDoubleBE(buf: Uint8Array, pos: number): number;
